import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { Wine, WineType } from './entities/wine.entity';
import { CreateWineDto } from './dto/create-wine.dto';
import { UpdateWineDto } from './dto/update-wine.dto';
import { UserRole } from '@/modules/users/entities/user.entity';
import { LoggerService } from '@/common/services/logger.service';

@Injectable()
export class WineService {
  constructor(
    @InjectModel(Wine)
    private wineModel: typeof Wine,
    private logger: LoggerService,
  ) { }

  async create(createWineDto: CreateWineDto, userId: string): Promise<Wine> {
    try {
      // Check for duplicate wine (same name, producer, vintage)
      const existingWine = await this.wineModel.findOne({
        where: {
          name: createWineDto.name,
          producer: createWineDto.producer,
          vintage: createWineDto.vintage,
        },
      });

      if (existingWine) {
        throw new ConflictException(
          'A wine with the same name, producer, and vintage already exists',
        );
      }

      // Calculate average rating if ratings are provided
      let averageRating = null;
      if (createWineDto.ratings) {
        const ratings = Object.values(createWineDto.ratings).filter(
          (rating) => typeof rating === 'number',
        ) as number[];

        if (ratings.length > 0) {
          averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
        }
      }

      const wine = await this.wineModel.create({
        ...createWineDto,
        averageRating,
        currency: createWineDto.currency || 'USD',
      });

      this.logger.log(
        `Wine created: ${wine.name} ${wine.vintage} by user ${userId}`,
        'WineService',
      );

      return wine;
    } catch (error) {
      this.logger.error(
        `Failed to create wine: ${error.message}`,
        error.stack,
        'WineService',
      );
      throw error;
    }
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    type?: WineType,
    country?: string,
  ) {
    const offset = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { producer: { [Op.iLike]: `%${search}%` } },
        { region: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (country) {
      where.country = { [Op.iLike]: `%${country}%` };
    }

    const { rows: wines, count: total } = await this.wineModel.findAndCountAll({
      where,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
    });

    return {
      wines,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<Wine> {
    const wine = await this.wineModel.findByPk(id);
    if (!wine) {
      throw new NotFoundException('Wine not found');
    }
    return wine;
  }

  async update(id: string, updateWineDto: UpdateWineDto, userId: string): Promise<Wine> {
    const wine = await this.findOne(id);
    await wine.update(updateWineDto);
    this.logger.log(`Wine updated: ${wine.name} by user ${userId}`, 'WineService');
    return wine;
  }

  async remove(id: string, userId: string): Promise<void> {
    const wine = await this.findOne(id);
    await wine.destroy();
    this.logger.log(`Wine deleted: ${wine.name} by user ${userId}`, 'WineService');
  }
}
