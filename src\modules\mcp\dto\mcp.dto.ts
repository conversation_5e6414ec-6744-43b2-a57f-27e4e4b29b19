import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsObject, IsA<PERSON>y, IsOptional, IsBoolean } from 'class-validator';

export class McpServerCapabilitiesDto {
  @ApiProperty({
    description: 'Logging capabilities',
    example: {},
    required: false,
  })
  @IsOptional()
  @IsObject()
  logging?: {};

  @ApiProperty({
    description: 'Prompts capabilities',
    example: { listChanged: true },
    required: false,
  })
  @IsOptional()
  @IsObject()
  prompts?: {
    listChanged?: boolean;
  };

  @ApiProperty({
    description: 'Resources capabilities',
    example: { subscribe: true, listChanged: true },
    required: false,
  })
  @IsOptional()
  @IsObject()
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };

  @ApiProperty({
    description: 'Tools capabilities',
    example: { listChanged: true },
    required: false,
  })
  @IsOptional()
  @IsObject()
  tools?: {
    listChanged?: boolean;
  };
}

export class McpServerInfoDto {
  @ApiProperty({
    description: 'MCP server name',
    example: 'United Cellars Valuation API',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'MCP server version',
    example: '1.0.0',
  })
  @IsString()
  version: string;

  @ApiProperty({
    description: 'Server capabilities',
    type: McpServerCapabilitiesDto,
  })
  @IsObject()
  capabilities: McpServerCapabilitiesDto;
}

export class McpToolInputSchemaDto {
  @ApiProperty({
    description: 'Schema type',
    example: 'object',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Schema properties',
    example: {
      query: {
        type: 'string',
        description: 'Search query',
      },
    },
  })
  @IsObject()
  properties: Record<string, any>;

  @ApiProperty({
    description: 'Required properties',
    example: ['query'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  required?: string[];
}

export class McpToolDto {
  @ApiProperty({
    description: 'Tool name',
    example: 'search_wines',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Tool description',
    example: 'Search for wines in the database',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Input schema for the tool',
    type: McpToolInputSchemaDto,
  })
  @IsObject()
  inputSchema: McpToolInputSchemaDto;
}

export class McpToolsListDto {
  @ApiProperty({
    description: 'List of available tools',
    type: [McpToolDto],
  })
  @IsArray()
  tools: McpToolDto[];
}

export class McpResourceDto {
  @ApiProperty({
    description: 'Resource URI',
    example: 'wines://database',
  })
  @IsString()
  uri: string;

  @ApiProperty({
    description: 'Resource name',
    example: 'Wine Database',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Resource description',
    example: 'Access to the complete wine database',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'MIME type',
    example: 'application/json',
    required: false,
  })
  @IsOptional()
  @IsString()
  mimeType?: string;
}

export class McpResourcesListDto {
  @ApiProperty({
    description: 'List of available resources',
    type: [McpResourceDto],
  })
  @IsArray()
  resources: McpResourceDto[];
}

export class McpPromptArgumentDto {
  @ApiProperty({
    description: 'Argument name',
    example: 'preferences',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Argument description',
    example: 'User preferences for wine recommendation',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Whether the argument is required',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;
}

export class McpPromptDto {
  @ApiProperty({
    description: 'Prompt name',
    example: 'wine_recommendation',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Prompt description',
    example: 'Get wine recommendations based on preferences',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Prompt arguments',
    type: [McpPromptArgumentDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  arguments?: McpPromptArgumentDto[];
}

export class McpPromptsListDto {
  @ApiProperty({
    description: 'List of available prompts',
    type: [McpPromptDto],
  })
  @IsArray()
  prompts: McpPromptDto[];
}

export class McpToolCallDto {
  @ApiProperty({
    description: 'Name of the tool to call',
    example: 'search_wines',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Arguments for the tool',
    example: {
      query: 'Château Margaux',
      type: 'red',
      limit: 5,
    },
  })
  @IsObject()
  arguments: Record<string, any>;
}

export class McpContentDto {
  @ApiProperty({
    description: 'Content type',
    example: 'text',
    enum: ['text', 'image', 'resource'],
  })
  @IsString()
  type: 'text' | 'image' | 'resource';

  @ApiProperty({
    description: 'Text content',
    example: 'Search results: ...',
    required: false,
  })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiProperty({
    description: 'Binary data (base64 encoded)',
    required: false,
  })
  @IsOptional()
  @IsString()
  data?: string;

  @ApiProperty({
    description: 'MIME type for binary data',
    example: 'image/png',
    required: false,
  })
  @IsOptional()
  @IsString()
  mimeType?: string;
}

export class McpToolResultDto {
  @ApiProperty({
    description: 'Result content',
    type: [McpContentDto],
  })
  @IsArray()
  content: McpContentDto[];

  @ApiProperty({
    description: 'Whether the result is an error',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isError?: boolean;
}
