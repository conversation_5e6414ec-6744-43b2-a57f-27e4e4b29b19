import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@/common/dto/api-response.dto';

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  lastName: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User phone number',
    example: '+1234567890',
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: 'User role',
    example: 'user',
    enum: ['admin', 'user', 'appraiser'],
  })
  role: string;

  @ApiProperty({
    description: 'User status',
    example: 'active',
    enum: ['active', 'inactive', 'suspended'],
  })
  status: string;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2024-08-08T10:30:00.000Z',
    required: false,
  })
  lastLoginAt?: string;

  @ApiProperty({
    description: 'User preferences',
    example: { theme: 'dark', notifications: true },
    required: false,
  })
  preferences?: Record<string, any>;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  updatedAt: string;
}

export class UsersListResponseDto extends PaginatedResponseDto<UserResponseDto> {
  @ApiProperty({
    description: 'Array of users',
    type: [UserResponseDto],
  })
  users: UserResponseDto[];
}

export class UserStatsDto {
  @ApiProperty({
    description: 'Total number of users',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Users by status',
    example: {
      active: 120,
      inactive: 20,
      suspended: 10,
    },
  })
  byStatus: {
    active: number;
    inactive: number;
    suspended: number;
  };

  @ApiProperty({
    description: 'Users by role',
    example: {
      admins: 5,
      users: 130,
      appraisers: 15,
    },
  })
  byRole: {
    admins: number;
    users: number;
    appraisers: number;
  };
}
