import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@/common/dto/api-response.dto';

export class WineResponseDto {
  @ApiProperty({
    description: 'Wine ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Wine name',
    example: 'Château Margaux',
  })
  name: string;

  @ApiProperty({
    description: 'Wine producer',
    example: 'Château Margaux',
  })
  producer: string;

  @ApiProperty({
    description: 'Wine region',
    example: 'Margaux',
  })
  region: string;

  @ApiProperty({
    description: 'Country of origin',
    example: 'France',
  })
  country: string;

  @ApiProperty({
    description: 'Wine vintage year',
    example: 2010,
  })
  vintage: number;

  @ApiProperty({
    description: 'Type of wine',
    example: 'red',
    enum: ['red', 'white', 'rose', 'sparkling', 'dessert', 'fortified'],
  })
  type: string;

  @ApiProperty({
    description: 'Wine appellation',
    example: 'Margaux AOC',
    required: false,
  })
  appellation?: string;

  @ApiProperty({
    description: 'Wine classification',
    example: 'Premier Grand Cru Classé',
    required: false,
  })
  classification?: string;

  @ApiProperty({
    description: 'Alcohol content percentage',
    example: 13.5,
    required: false,
  })
  alcoholContent?: number;

  @ApiProperty({
    description: 'Bottle size',
    example: '750ml',
    required: false,
  })
  bottleSize?: string;

  @ApiProperty({
    description: 'Wine description',
    example: 'One of the most prestigious wines from Bordeaux...',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Tasting notes',
    example: {
      color: 'Deep ruby red',
      aroma: 'Blackcurrant, cedar, tobacco',
      palate: 'Full-bodied, silky tannins',
    },
    required: false,
  })
  tastingNotes?: Record<string, any>;

  @ApiProperty({
    description: 'Professional ratings',
    example: {
      parker: 98,
      wine_spectator: 96,
      jancis_robinson: 19,
    },
    required: false,
  })
  ratings?: Record<string, any>;

  @ApiProperty({
    description: 'Average rating',
    example: 97.7,
    required: false,
  })
  averageRating?: number;

  @ApiProperty({
    description: 'Current market price',
    example: 850.00,
    required: false,
  })
  marketPrice?: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    required: false,
  })
  currency?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  updatedAt: string;
}

export class WinesListResponseDto extends PaginatedResponseDto<WineResponseDto> {
  @ApiProperty({
    description: 'Array of wines',
    type: [WineResponseDto],
  })
  wines: WineResponseDto[];
}
