import { Module } from '@nestjs/common';
import { McpService } from './mcp.service';
import { Mcp<PERSON>ontroller } from './mcp.controller';
import { McpGateway } from './mcp.gateway';
import { UsersModule } from '@/modules/users/users.module';
import { ValuationModule } from '@/modules/valuation/valuation.module';

@Module({
  imports: [UsersModule, ValuationModule],
  controllers: [McpController],
  providers: [McpService, McpGateway],
  exports: [McpService],
})
export class McpModule {}
