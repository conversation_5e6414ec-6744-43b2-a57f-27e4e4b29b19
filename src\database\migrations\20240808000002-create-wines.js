'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('wines', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      producer: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      region: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      country: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      vintage: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('red', 'white', 'rose', 'sparkling', 'dessert', 'fortified'),
        allowNull: false,
      },
      appellation: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      classification: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      alcohol_content: {
        type: Sequelize.DECIMAL(3, 1),
        allowNull: true,
      },
      bottle_size: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      tasting_notes: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      ratings: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      average_rating: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      market_price: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: true,
        defaultValue: 'USD',
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex('wines', ['name']);
    await queryInterface.addIndex('wines', ['producer']);
    await queryInterface.addIndex('wines', ['region']);
    await queryInterface.addIndex('wines', ['country']);
    await queryInterface.addIndex('wines', ['vintage']);
    await queryInterface.addIndex('wines', ['type']);
    await queryInterface.addIndex('wines', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('wines');
  },
};
