import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  AllowNull,
  CreatedAt,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { User } from '@/modules/users/entities/user.entity';
import { Valuation } from './valuation.entity';

export enum HistoryAction {
  CREATED = 'created',
  UPDATED = 'updated',
  STATUS_CHANGED = 'status_changed',
  ASSIGNED = 'assigned',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  COMMENT_ADDED = 'comment_added',
}

export enum ValuationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Table({
  tableName: 'valuation_history',
  timestamps: false,
})
export class ValuationHistory extends Model<ValuationHistory> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @ForeignKey(() => Valuation)
  @AllowNull(false)
  @Column(DataType.UUID)
  valuationId: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  userId: string;

  @AllowNull(false)
  @Column(DataType.ENUM(...Object.values(HistoryAction)))
  action: HistoryAction;

  @Column(DataType.STRING(255))
  description: string;

  @Column(DataType.ENUM('draft', 'pending', 'completed', 'cancelled'))
  oldStatus: string;

  @Column(DataType.ENUM('draft', 'pending', 'completed', 'cancelled'))
  newStatus: string;

  @Column(DataType.JSON)
  changes: Record<string, any>;

  @Column(DataType.TEXT)
  comment: string;

  @CreatedAt
  createdAt: Date;

  // Associations
  @BelongsTo(() => Valuation)
  valuation: Valuation;

  @BelongsTo(() => User)
  user: User;
}
