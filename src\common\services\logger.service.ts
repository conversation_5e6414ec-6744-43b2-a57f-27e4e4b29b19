import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    this.createLogger();
  }

  private createLogger() {
    const logLevel = this.configService.get<string>('LOG_LEVEL', 'info');
    const logFileEnabled = this.configService.get<boolean>('LOG_FILE_ENABLED', true);
    const logConsoleEnabled = this.configService.get<boolean>('LOG_CONSOLE_ENABLED', true);

    const transports: winston.transport[] = [];

    // Console transport
    if (logConsoleEnabled) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.printf(({ timestamp, level, message, context, ...meta }) => {
              const contextStr = context ? `[${context}] ` : '';
              const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
              return `${timestamp} ${level}: ${contextStr}${message}${metaStr}`;
            }),
          ),
        }),
      );
    }

    // File transport
    if (logFileEnabled) {
      transports.push(
        new DailyRotateFile({
          filename: 'logs/application-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '14d',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      );

      transports.push(
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: '20m',
          maxFiles: '30d',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      transports,
    });
  }

  log(message: string, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: string, trace?: string, context?: string) {
    this.logger.error(message, { trace, context });
  }

  warn(message: string, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: string, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: string, context?: string) {
    this.logger.verbose(message, { context });
  }

  // Additional methods for structured logging
  logWithMeta(level: string, message: string, meta: any, context?: string) {
    this.logger.log(level, message, { ...meta, context });
  }

  logRequest(method: string, url: string, statusCode: number, responseTime: number, context?: string) {
    this.logger.info(`${method} ${url} ${statusCode} - ${responseTime}ms`, { 
      method, 
      url, 
      statusCode, 
      responseTime, 
      context: context || 'HTTP' 
    });
  }

  logError(error: Error, context?: string) {
    this.logger.error(error.message, {
      stack: error.stack,
      name: error.name,
      context: context || 'Error',
    });
  }
}
