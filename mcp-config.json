{"mcpServers": {"united-cellars": {"command": "node", "args": ["dist/main.js"], "env": {"NODE_ENV": "production", "MCP_MODE": "true"}}}, "server": {"name": "United Cellars Valuation API", "version": "1.0.0", "description": "MCP server for wine valuation and management", "capabilities": {"tools": true, "resources": true, "prompts": true, "logging": true}, "endpoints": {"http": "http://localhost:3000/api/v1/mcp", "websocket": "ws://localhost:3001/mcp"}}, "tools": [{"name": "search_wines", "description": "Search for wines in the database", "category": "search"}, {"name": "get_wine_details", "description": "Get detailed wine information", "category": "retrieval"}, {"name": "get_valuations", "description": "Get valuation requests", "category": "retrieval"}, {"name": "get_valuation_details", "description": "Get detailed valuation information", "category": "retrieval"}, {"name": "get_user_stats", "description": "Get user statistics", "category": "analytics"}, {"name": "create_wine", "description": "Add a new wine to the database", "category": "creation"}], "resources": [{"uri": "wines://database", "name": "Wine Database", "description": "Complete wine catalog with search capabilities"}, {"uri": "valuations://database", "name": "Valuation Database", "description": "Wine valuation requests and history"}, {"uri": "users://database", "name": "User Database", "description": "User information and statistics"}, {"uri": "api://documentation", "name": "API Documentation", "description": "Complete API documentation"}], "prompts": [{"name": "wine_recommendation", "description": "Get wine recommendations based on preferences"}, {"name": "valuation_analysis", "description": "Analyze wine valuation data"}, {"name": "market_trends", "description": "Analyze market trends for wine categories"}]}