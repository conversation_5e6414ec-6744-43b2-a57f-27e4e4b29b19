'use strict';

const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('wines', [
      {
        id: uuidv4(),
        name: 'Château Margaux',
        producer: 'Château Margaux',
        region: 'Margaux',
        country: 'France',
        vintage: 2010,
        type: 'red',
        appellation: 'Margaux AOC',
        classification: 'Premier Grand Cru Classé',
        alcohol_content: 13.5,
        bottle_size: '750ml',
        description: 'One of the most prestigious wines from Bordeaux, known for its elegance and complexity.',
        tasting_notes: JSON.stringify({
          color: 'Deep ruby red',
          aroma: 'Blackcurrant, cedar, tobacco, violet',
          palate: 'Full-bodied, silky tannins, long finish',
          serving_temp: '16-18°C',
        }),
        ratings: JSON.stringify({
          parker: 98,
          wine_spectator: 96,
          jancis_robinson: 19,
        }),
        average_rating: 97.7,
        market_price: 850.00,
        currency: 'USD',
        metadata: JSON.stringify({
          harvest_date: '2010-09-15',
          bottling_date: '2012-06-20',
          production: 130000,
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Dom Pérignon',
        producer: 'Moët & Chandon',
        region: 'Champagne',
        country: 'France',
        vintage: 2012,
        type: 'sparkling',
        appellation: 'Champagne AOC',
        classification: 'Prestige Cuvée',
        alcohol_content: 12.5,
        bottle_size: '750ml',
        description: 'The most famous champagne in the world, representing the pinnacle of luxury.',
        tasting_notes: JSON.stringify({
          color: 'Golden yellow with fine bubbles',
          aroma: 'White flowers, citrus, brioche, mineral',
          palate: 'Creamy texture, fresh acidity, long mineral finish',
          serving_temp: '6-8°C',
        }),
        ratings: JSON.stringify({
          wine_spectator: 95,
          decanter: 96,
          wine_enthusiast: 94,
        }),
        average_rating: 95.0,
        market_price: 220.00,
        currency: 'USD',
        metadata: JSON.stringify({
          harvest_date: '2012-09-10',
          disgorgement_date: '2020-03-15',
          dosage: '6g/L',
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: uuidv4(),
        name: 'Opus One',
        producer: 'Opus One Winery',
        region: 'Napa Valley',
        country: 'United States',
        vintage: 2018,
        type: 'red',
        appellation: 'Napa Valley AVA',
        classification: 'Bordeaux Blend',
        alcohol_content: 14.5,
        bottle_size: '750ml',
        description: 'A joint venture between Robert Mondavi and Baron Philippe de Rothschild.',
        tasting_notes: JSON.stringify({
          color: 'Deep purple-black',
          aroma: 'Cassis, dark chocolate, cedar, graphite',
          palate: 'Full-bodied, structured tannins, balanced acidity',
          serving_temp: '16-18°C',
        }),
        ratings: JSON.stringify({
          parker: 96,
          wine_spectator: 94,
          wine_advocate: 95,
        }),
        average_rating: 95.0,
        market_price: 420.00,
        currency: 'USD',
        metadata: JSON.stringify({
          harvest_date: '2018-09-20',
          bottling_date: '2020-09-15',
          blend: 'Cabernet Sauvignon 84%, Petit Verdot 6%, Merlot 5%, Cabernet Franc 3%, Malbec 2%',
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('wines', {
      name: {
        [Sequelize.Op.in]: [
          'Château Margaux',
          'Dom Pérignon',
          'Opus One',
        ],
      },
    });
  },
};
