# United Cellars API - Complete Development Guide

## 📚 Table of Contents
1. [API Usage Guide](#api-usage-guide)
2. [Authentication](#authentication)
3. [MCP (Model Context Protocol)](#mcp-model-context-protocol)
4. [Creating New Modules](#creating-new-modules)
5. [Database Migrations](#database-migrations)
6. [Seeders](#seeders)
7. [Controllers](#controllers)
8. [Services](#services)
9. [DTOs (Data Transfer Objects)](#dtos-data-transfer-objects)
10. [Best Practices](#best-practices)

---

## 🚀 API Usage Guide

### Base URL
```
http://localhost:3000/api/v1
```

### API Documentation
Interactive Swagger documentation is available at:
```
http://localhost:3000/api/docs
```

### Response Format
All API responses follow this standard format:
```json
{
  "success": true,
  "statusCode": 200,
  "timestamp": "2024-08-08T10:30:00.000Z",
  "path": "/api/v1/users",
  "method": "GET",
  "data": { /* actual response data */ },
  "message": "Optional message"
}
```

### Error Response Format
```json
{
  "success": false,
  "statusCode": 400,
  "timestamp": "2024-08-08T10:30:00.000Z",
  "path": "/api/v1/users",
  "method": "POST",
  "message": "Validation failed",
  "errors": { /* detailed error information */ }
}
```

---

## 🔐 Authentication

### 1. User Registration
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!",
  "phone": "+1234567890"
}
```

### 2. User Login
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 86400
}
```

### 3. Using JWT Token
Include the token in all authenticated requests:
```bash
Authorization: Bearer <your-jwt-token>
```

### 4. Refresh Token
```bash
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

---

## 🔌 MCP (Model Context Protocol)

The United Cellars API includes a built-in MCP server that allows AI assistants and other MCP clients to interact with the wine valuation system programmatically.

### MCP Endpoints

#### Server Information
```bash
GET /api/v1/mcp/info
```
Returns server capabilities and metadata.

#### Available Tools
```bash
GET /api/v1/mcp/tools
```
Lists all available tools that can be called via MCP.

#### Available Resources
```bash
GET /api/v1/mcp/resources
```
Lists all available resources that can be accessed.

#### Available Prompts
```bash
GET /api/v1/mcp/prompts
```
Lists all available prompt templates.

### MCP Tool Execution

#### Authenticated Tool Call
```bash
POST /api/v1/mcp/tools/call
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "search_wines",
  "arguments": {
    "query": "Château Margaux",
    "type": "red",
    "limit": 5
  }
}
```

#### Public Tool Call (Read-only)
```bash
POST /api/v1/mcp/tools/call/public
Content-Type: application/json

{
  "name": "search_wines",
  "arguments": {
    "query": "Bordeaux",
    "limit": 10
  }
}
```

### Available MCP Tools

1. **search_wines** - Search for wines in the database
2. **get_wine_details** - Get detailed wine information
3. **get_valuations** - Get valuation requests
4. **get_valuation_details** - Get detailed valuation information
5. **get_user_stats** - Get user statistics (admin only)
6. **create_wine** - Add a new wine (authenticated)

### WebSocket Connection

The MCP server also supports WebSocket connections at `ws://localhost:3001/mcp` for real-time communication with AI assistants.

### Integration with AI Assistants

To integrate with AI assistants like Claude Desktop, add the server configuration to your MCP settings using the provided `mcp-config.json` file.

---

## 🏗️ Creating New Modules

### Step 1: Generate Module Structure
```bash
# Generate a new module
nest generate module modules/inventory

# Generate controller
nest generate controller modules/inventory

# Generate service
nest generate service modules/inventory
```

### Step 2: Create Module File
```typescript
// src/modules/inventory/inventory.module.ts
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { InventoryController } from './inventory.controller';
import { InventoryService } from './inventory.service';
import { InventoryItem } from './entities/inventory-item.entity';

@Module({
  imports: [SequelizeModule.forFeature([InventoryItem])],
  controllers: [InventoryController],
  providers: [InventoryService],
  exports: [InventoryService],
})
export class InventoryModule {}
```

### Step 3: Create Entity
```typescript
// src/modules/inventory/entities/inventory-item.entity.ts
import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Wine } from '@/modules/valuation/entities/wine.entity';
import { User } from '@/modules/users/entities/user.entity';

@Table({
  tableName: 'inventory_items',
  timestamps: true,
  paranoid: true,
})
export class InventoryItem extends Model<InventoryItem> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  userId: string;

  @ForeignKey(() => Wine)
  @AllowNull(false)
  @Column(DataType.UUID)
  wineId: string;

  @AllowNull(false)
  @Column(DataType.INTEGER)
  quantity: number;

  @Column(DataType.STRING(255))
  location: string;

  @Column(DataType.DECIMAL(10, 2))
  purchasePrice: number;

  @Column(DataType.DATE)
  purchaseDate: Date;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;

  @DeletedAt
  deletedAt: Date;

  // Associations
  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Wine)
  wine: Wine;
}
```

### Step 4: Register Module in App Module
```typescript
// src/app.module.ts
import { InventoryModule } from '@/modules/inventory/inventory.module';

@Module({
  imports: [
    // ... other imports
    InventoryModule,
  ],
  // ...
})
export class AppModule {}
```

---

## 🗄️ Database Migrations

### Creating a New Migration
```bash
# Generate migration file
npm run migration:generate -- --name create-inventory-items
```

### Migration File Structure
```javascript
// src/database/migrations/YYYYMMDDHHMMSS-create-inventory-items.js
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('inventory_items', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      wine_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'wines',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      location: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      purchase_price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      purchase_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex('inventory_items', ['user_id']);
    await queryInterface.addIndex('inventory_items', ['wine_id']);
    await queryInterface.addIndex('inventory_items', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('inventory_items');
  },
};
```

### Running Migrations
```bash
# Run all pending migrations
npm run migration:run

# Revert last migration
npm run migration:revert
```

---

## 🌱 Seeders

### Creating a Seeder
```bash
# Generate seeder file
npx sequelize-cli seed:generate --name demo-inventory-items
```

### Seeder File Structure
```javascript
// src/database/seeders/YYYYMMDDHHMMSS-demo-inventory-items.js
'use strict';

const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Get existing user and wine IDs
    const users = await queryInterface.sequelize.query(
      'SELECT id FROM users LIMIT 1',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    const wines = await queryInterface.sequelize.query(
      'SELECT id FROM wines LIMIT 3',
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (users.length > 0 && wines.length > 0) {
      await queryInterface.bulkInsert('inventory_items', [
        {
          id: uuidv4(),
          user_id: users[0].id,
          wine_id: wines[0].id,
          quantity: 6,
          location: 'Cellar A, Rack 1, Shelf 3',
          purchase_price: 850.00,
          purchase_date: new Date('2023-06-15'),
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          id: uuidv4(),
          user_id: users[0].id,
          wine_id: wines[1].id,
          quantity: 12,
          location: 'Cellar B, Rack 2, Shelf 1',
          purchase_price: 220.00,
          purchase_date: new Date('2023-08-20'),
          created_at: new Date(),
          updated_at: new Date(),
        },
      ]);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('inventory_items', null, {});
  },
};
```

### Running Seeders
```bash
# Run all seeders
npm run seed:run

# Revert all seeders
npm run seed:revert
```

---

## 🎮 Controllers

### Controller Structure
```typescript
// src/modules/inventory/inventory.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { InventoryService } from './inventory.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
import { InventoryItemResponseDto } from './dto/inventory-item-response.dto';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/users/entities/user.entity';

@ApiTags('Inventory')
@ApiBearerAuth('JWT-auth')
@UseGuards(ThrottlerGuard, JwtAuthGuard)
@Controller({ path: 'inventory', version: '1' })
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Add wine to inventory',
    description: 'Add a wine item to user inventory with quantity and location'
  })
  @ApiBody({ type: CreateInventoryItemDto })
  @ApiResponse({ 
    status: 201, 
    description: 'Inventory item created successfully',
    type: InventoryItemResponseDto,
  })
  create(@Body() createInventoryItemDto: CreateInventoryItemDto, @Request() req) {
    return this.inventoryService.create(createInventoryItemDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get user inventory items' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, description: 'Inventory items retrieved successfully' })
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Request() req,
  ) {
    return this.inventoryService.findAll(page, limit, req.user.id, req.user.role);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get inventory item by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Inventory item retrieved successfully' })
  findOne(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.inventoryService.findOne(id, req.user.id, req.user.role);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update inventory item' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiBody({ type: UpdateInventoryItemDto })
  @ApiResponse({ status: 200, description: 'Inventory item updated successfully' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateInventoryItemDto: UpdateInventoryItemDto,
    @Request() req,
  ) {
    return this.inventoryService.update(id, updateInventoryItemDto, req.user.id, req.user.role);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove inventory item' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Inventory item removed successfully' })
  remove(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.inventoryService.remove(id, req.user.id, req.user.role);
  }
}
```

---

## 🔧 Services

### Service Structure
```typescript
// src/modules/inventory/inventory.service.ts
import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { InventoryItem } from './entities/inventory-item.entity';
import { Wine } from '@/modules/valuation/entities/wine.entity';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
import { LoggerService } from '@/common/services/logger.service';

@Injectable()
export class InventoryService {
  constructor(
    @InjectModel(InventoryItem)
    private inventoryItemModel: typeof InventoryItem,
    private logger: LoggerService,
  ) {}

  async create(createInventoryItemDto: CreateInventoryItemDto, userId: string): Promise<InventoryItem> {
    try {
      const inventoryItem = await this.inventoryItemModel.create({
        ...createInventoryItemDto,
        userId,
        purchaseDate: createInventoryItemDto.purchaseDate ? new Date(createInventoryItemDto.purchaseDate) : null,
      });

      this.logger.log(`Inventory item created: ${inventoryItem.id} by user ${userId}`, 'InventoryService');
      
      return this.findOne(inventoryItem.id, userId, UserRole.USER);
    } catch (error) {
      this.logger.error(`Failed to create inventory item: ${error.message}`, error.stack, 'InventoryService');
      throw error;
    }
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    userId: string,
    userRole: UserRole,
  ) {
    const offset = (page - 1) * limit;
    const where: any = {};

    // Users can only see their own inventory
    if (userRole === UserRole.USER) {
      where.userId = userId;
    }

    const { rows: items, count: total } = await this.inventoryItemModel.findAndCountAll({
      where,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        { model: Wine, as: 'wine' },
        { model: User, as: 'user', attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
    });

    return {
      items,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, userId: string, userRole: UserRole): Promise<InventoryItem> {
    const inventoryItem = await this.inventoryItemModel.findByPk(id, {
      include: [
        { model: Wine, as: 'wine' },
        { model: User, as: 'user', attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
    });

    if (!inventoryItem) {
      throw new NotFoundException('Inventory item not found');
    }

    // Check access permissions
    if (userRole === UserRole.USER && inventoryItem.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return inventoryItem;
  }

  async update(
    id: string,
    updateInventoryItemDto: UpdateInventoryItemDto,
    userId: string,
    userRole: UserRole,
  ): Promise<InventoryItem> {
    const inventoryItem = await this.findOne(id, userId, userRole);

    // Check if user can update
    if (userRole === UserRole.USER && inventoryItem.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    const updateData = {
      ...updateInventoryItemDto,
      ...(updateInventoryItemDto.purchaseDate && { 
        purchaseDate: new Date(updateInventoryItemDto.purchaseDate) 
      }),
    };

    await inventoryItem.update(updateData);
    
    this.logger.log(`Inventory item updated: ${inventoryItem.id} by user ${userId}`, 'InventoryService');
    
    return this.findOne(inventoryItem.id, userId, userRole);
  }

  async remove(id: string, userId: string, userRole: UserRole): Promise<void> {
    const inventoryItem = await this.findOne(id, userId, userRole);

    if (userRole === UserRole.USER && inventoryItem.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    await inventoryItem.destroy();
    this.logger.log(`Inventory item deleted: ${inventoryItem.id} by user ${userId}`, 'InventoryService');
  }
}
```

---

## 📝 DTOs (Data Transfer Objects)

### Create DTO
```typescript
// src/modules/inventory/dto/create-inventory-item.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsPositive,
  IsUUID,
  MaxLength,
  IsDateString,
} from 'class-validator';

export class CreateInventoryItemDto {
  @ApiProperty({
    description: 'Wine ID to add to inventory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  wineId: string;

  @ApiProperty({
    description: 'Quantity of bottles',
    example: 6,
    minimum: 1,
  })
  @IsNumber()
  @IsPositive()
  quantity: number;

  @ApiProperty({
    description: 'Storage location',
    example: 'Cellar A, Rack 1, Shelf 3',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  location?: string;

  @ApiProperty({
    description: 'Purchase price per bottle',
    example: 850.00,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  purchasePrice?: number;

  @ApiProperty({
    description: 'Purchase date',
    example: '2023-06-15',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  purchaseDate?: string;
}
```

### Update DTO
```typescript
// src/modules/inventory/dto/update-inventory-item.dto.ts
import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateInventoryItemDto } from './create-inventory-item.dto';

export class UpdateInventoryItemDto extends PartialType(
  OmitType(CreateInventoryItemDto, ['wineId'] as const),
) {}
```

### Response DTO
```typescript
// src/modules/inventory/dto/inventory-item-response.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { WineResponseDto } from '@/modules/valuation/dto/wine-response.dto';
import { UserResponseDto } from '@/modules/users/dto/user-response.dto';

export class InventoryItemResponseDto {
  @ApiProperty({
    description: 'Inventory item ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Quantity of bottles',
    example: 6,
  })
  quantity: number;

  @ApiProperty({
    description: 'Storage location',
    example: 'Cellar A, Rack 1, Shelf 3',
    required: false,
  })
  location?: string;

  @ApiProperty({
    description: 'Purchase price per bottle',
    example: 850.00,
    required: false,
  })
  purchasePrice?: number;

  @ApiProperty({
    description: 'Purchase date',
    example: '2023-06-15T00:00:00.000Z',
    required: false,
  })
  purchaseDate?: string;

  @ApiProperty({
    description: 'Wine details',
    type: WineResponseDto,
  })
  wine: WineResponseDto;

  @ApiProperty({
    description: 'Owner details',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  updatedAt: string;
}
```

---

## 🎯 Best Practices

### 1. Module Organization
- Keep modules focused on a single domain
- Use barrel exports for clean imports
- Follow the feature module pattern
- Separate shared modules from feature modules

### 2. Database Design
- Always use UUIDs for primary keys
- Include `created_at`, `updated_at`, and `deleted_at` timestamps
- Use proper foreign key constraints
- Add database indexes for frequently queried columns
- Use enum types for status fields

### 3. API Design
- Use consistent HTTP status codes
- Implement proper error handling
- Use DTOs for request/response validation
- Include comprehensive Swagger documentation
- Implement pagination for list endpoints
- Use proper HTTP methods (GET, POST, PUT, PATCH, DELETE)

### 4. Security
- Always validate input data
- Use role-based access control
- Implement rate limiting
- Use JWT tokens for authentication
- Hash passwords with bcrypt
- Validate UUIDs in route parameters

### 5. Error Handling
```typescript
// Custom exception example
import { HttpException, HttpStatus } from '@nestjs/common';

export class InventoryItemNotFoundException extends HttpException {
  constructor(id: string) {
    super(`Inventory item with ID ${id} not found`, HttpStatus.NOT_FOUND);
  }
}
```

### 6. Logging
```typescript
// Service logging example
this.logger.log(`Operation completed: ${operation}`, 'ServiceName');
this.logger.error(`Operation failed: ${error.message}`, error.stack, 'ServiceName');
this.logger.warn(`Warning: ${warning}`, 'ServiceName');
```

### 7. Testing
```typescript
// Unit test example
describe('InventoryService', () => {
  let service: InventoryService;
  let model: typeof InventoryItem;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InventoryService,
        {
          provide: getModelToken(InventoryItem),
          useValue: mockInventoryItemModel,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<InventoryService>(InventoryService);
    model = module.get<typeof InventoryItem>(getModelToken(InventoryItem));
  });

  it('should create an inventory item', async () => {
    const createDto = { wineId: 'wine-id', quantity: 6 };
    const result = await service.create(createDto, 'user-id');
    expect(result).toBeDefined();
  });
});
```

### 8. Environment Configuration
```typescript
// Configuration validation
import { IsString, IsNumber, IsBoolean } from 'class-validator';

export class EnvironmentVariables {
  @IsString()
  DATABASE_URL: string;

  @IsNumber()
  PORT: number;

  @IsString()
  JWT_SECRET: string;

  @IsBoolean()
  DEBUG: boolean;
}
```

---

## 🚀 Quick Commands Reference

```bash
# Development
npm run start:dev          # Start in development mode
npm run build             # Build the application
npm run start:prod        # Start in production mode

# Database
npm run migration:run     # Run migrations
npm run migration:revert  # Revert last migration
npm run seed:run         # Run seeders
npm run seed:revert      # Revert seeders

# Testing
npm run test             # Run unit tests
npm run test:e2e         # Run integration tests
npm run test:cov         # Run tests with coverage

# Code Quality
npm run lint             # Run ESLint
npm run format           # Format code with Prettier
```

---

## 📞 Support

For questions or issues:
1. Check the Swagger documentation at `/api/docs`
2. Review the application logs
3. Check the health endpoints for system status
4. Refer to the NestJS official documentation

---

**Happy Coding! 🎉**
```
