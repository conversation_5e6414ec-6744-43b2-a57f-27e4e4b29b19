import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsNumber,
  IsPositive,
  IsUUID,
  MaxLength,
  Min,
  IsDateString,
} from 'class-validator';
import { ValuationPurpose } from '../entities/valuation.entity';

export class CreateValuationDto {
  @ApiProperty({
    description: 'Wine ID to be valued',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  wineId: string;

  @ApiProperty({
    description: 'Purpose of valuation',
    enum: ValuationPurpose,
    example: ValuationPurpose.INSURANCE,
  })
  @IsEnum(ValuationPurpose)
  purpose: ValuationPurpose;

  @ApiProperty({
    description: 'Quantity of bottles',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @IsPositive()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Condition of the wine/bottles',
    example: 'Excellent - stored in professional cellar',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  condition?: string;

  @ApiProperty({
    description: 'Provenance information',
    example: 'Purchased directly from château in 2012',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  provenance?: string;

  @ApiProperty({
    description: 'Storage conditions',
    example: 'Temperature controlled cellar, 55°F, 70% humidity',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  storage?: string;

  @ApiProperty({
    description: 'Estimated value by user',
    example: 850.00,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  estimatedValue?: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    required: false,
    maxLength: 3,
  })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  currency?: string;

  @ApiProperty({
    description: 'Desired valuation date',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  valuationDate?: string;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Wine has been in my collection for 10 years...',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
