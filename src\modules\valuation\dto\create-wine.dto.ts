import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsNumber,
  IsPositive,
  Min,
  Max,
  MaxLength,
  IsObject,
  IsArray,
  IsDecimal,
} from 'class-validator';
import { WineType } from '../entities/wine.entity';
import { IsValidVintage, IsPositiveNumber } from '@/common/decorators/validation.decorators';

export class CreateWineDto {
  @ApiProperty({
    description: 'Wine name',
    example: 'Château Margaux',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Wine producer/winery',
    example: 'Château Margaux',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  producer: string;

  @ApiProperty({
    description: 'Wine region',
    example: 'Margaux',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  region: string;

  @ApiProperty({
    description: 'Country of origin',
    example: 'France',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  country: string;

  @ApiProperty({
    description: 'Wine vintage year',
    example: 2010,
    minimum: 1800,
  })
  @IsNumber()
  @IsValidVintage()
  vintage: number;

  @ApiProperty({
    description: 'Type of wine',
    enum: WineType,
    example: WineType.RED,
  })
  @IsEnum(WineType)
  type: WineType;

  @ApiProperty({
    description: 'Wine appellation',
    example: 'Margaux AOC',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  appellation?: string;

  @ApiProperty({
    description: 'Wine classification',
    example: 'Premier Grand Cru Classé',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  classification?: string;

  @ApiProperty({
    description: 'Alcohol content percentage',
    example: 13.5,
    required: false,
    minimum: 0,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(50)
  alcoholContent?: number;

  @ApiProperty({
    description: 'Bottle size',
    example: '750ml',
    required: false,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  bottleSize?: string;

  @ApiProperty({
    description: 'Wine description',
    example: 'One of the most prestigious wines from Bordeaux...',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Tasting notes object',
    example: {
      color: 'Deep ruby red',
      aroma: 'Blackcurrant, cedar, tobacco',
      palate: 'Full-bodied, silky tannins',
      finish: 'Long and elegant',
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  tastingNotes?: Record<string, any>;

  @ApiProperty({
    description: 'Professional ratings from various sources',
    example: {
      parker: 98,
      wine_spectator: 96,
      jancis_robinson: 19,
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  ratings?: Record<string, any>;

  @ApiProperty({
    description: 'Current market price',
    example: 850.00,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  marketPrice?: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    required: false,
    maxLength: 3,
  })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  currency?: string;

  @ApiProperty({
    description: 'Additional metadata',
    example: {
      harvest_date: '2010-09-15',
      bottling_date: '2012-06-20',
      production: 130000,
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
