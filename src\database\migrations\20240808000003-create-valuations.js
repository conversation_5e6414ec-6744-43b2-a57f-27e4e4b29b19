'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('valuations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      wine_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'wines',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      reference_number: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
      },
      status: {
        type: Sequelize.ENUM('draft', 'pending', 'completed', 'cancelled'),
        defaultValue: 'draft',
        allowNull: false,
      },
      purpose: {
        type: Sequelize.ENUM('insurance', 'sale', 'purchase', 'estate', 'donation', 'other'),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      condition: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      provenance: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      storage: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      estimated_value: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      final_value: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: true,
        defaultValue: 'USD',
      },
      valuation_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      expiry_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      methodology: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      market_data: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      attachments: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      appraiser_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      completed_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex('valuations', ['user_id']);
    await queryInterface.addIndex('valuations', ['wine_id']);
    await queryInterface.addIndex('valuations', ['appraiser_id']);
    await queryInterface.addIndex('valuations', ['reference_number']);
    await queryInterface.addIndex('valuations', ['status']);
    await queryInterface.addIndex('valuations', ['purpose']);
    await queryInterface.addIndex('valuations', ['created_at']);
    await queryInterface.addIndex('valuations', ['valuation_date']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('valuations');
  },
};
