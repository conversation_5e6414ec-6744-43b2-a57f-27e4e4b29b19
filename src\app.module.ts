import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { SequelizeModule } from '@nestjs/sequelize';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from '@/config/database/database.module';
import { LoggerModule } from '@/common/services/logger.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { UsersModule } from '@/modules/users/users.module';
import { ValuationModule } from '@/modules/valuation/valuation.module';
import { HealthModule } from '@/modules/health/health.module';
import { McpModule } from '@/modules/mcp/mcp.module';
import { MetricsModule } from '@/common/services/metrics.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        throttlers: [
          {
            ttl: parseInt(process.env.THROTTLE_TTL || '60') * 1000,
            limit: parseInt(process.env.THROTTLE_LIMIT || '100'),
          },
        ],
      }),
    }),

    // Database
    DatabaseModule,

    // Common services
    LoggerModule,
    MetricsModule,

    // Feature modules
    AuthModule,
    UsersModule,
    ValuationModule,
    HealthModule,
    McpModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
