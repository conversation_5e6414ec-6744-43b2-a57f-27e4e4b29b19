import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, WebSocket } from 'ws';
import { Logger } from '@nestjs/common';
import { McpService } from './mcp.service';
import { LoggerService } from '@/common/services/logger.service';

interface McpMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

@WebSocketGateway({
  port: 3001,
  path: '/mcp',
  transports: ['websocket'],
})
export class McpGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(McpGateway.name);
  private clients = new Map<WebSocket, { id: string; authenticated: boolean }>();

  constructor(
    private mcpService: McpService,
    private loggerService: LoggerService,
  ) {}

  handleConnection(client: WebSocket) {
    const clientId = this.generateClientId();
    this.clients.set(client, { id: clientId, authenticated: false });
    
    this.logger.log(`MCP client connected: ${clientId}`);
    this.loggerService.log(`MCP client connected: ${clientId}`, 'McpGateway');

    // Send server info on connection
    this.sendMessage(client, {
      jsonrpc: '2.0',
      method: 'server/info',
      params: this.mcpService.getServerInfo(),
    });
  }

  handleDisconnect(client: WebSocket) {
    const clientInfo = this.clients.get(client);
    if (clientInfo) {
      this.logger.log(`MCP client disconnected: ${clientInfo.id}`);
      this.loggerService.log(`MCP client disconnected: ${clientInfo.id}`, 'McpGateway');
      this.clients.delete(client);
    }
  }

  @SubscribeMessage('message')
  async handleMessage(
    @MessageBody() data: string,
    @ConnectedSocket() client: WebSocket,
  ) {
    try {
      const message: McpMessage = JSON.parse(data);
      await this.processMessage(client, message);
    } catch (error) {
      this.logger.error(`Failed to process MCP message: ${error.message}`);
      this.sendError(client, -32700, 'Parse error', null);
    }
  }

  private async processMessage(client: WebSocket, message: McpMessage) {
    const { method, params, id } = message;

    try {
      switch (method) {
        case 'initialize':
          await this.handleInitialize(client, params, id);
          break;

        case 'tools/list':
          this.sendResponse(client, id, {
            tools: this.mcpService.getAvailableTools(),
          });
          break;

        case 'tools/call':
          await this.handleToolCall(client, params, id);
          break;

        case 'resources/list':
          this.sendResponse(client, id, {
            resources: this.mcpService.getAvailableResources(),
          });
          break;

        case 'prompts/list':
          this.sendResponse(client, id, {
            prompts: this.mcpService.getAvailablePrompts(),
          });
          break;

        case 'ping':
          this.sendResponse(client, id, { pong: true });
          break;

        default:
          this.sendError(client, -32601, 'Method not found', id);
      }
    } catch (error) {
      this.logger.error(`MCP method execution failed: ${error.message}`);
      this.sendError(client, -32603, 'Internal error', id);
    }
  }

  private async handleInitialize(client: WebSocket, params: any, id: string | number) {
    const clientInfo = this.clients.get(client);
    if (clientInfo) {
      clientInfo.authenticated = true;
      this.clients.set(client, clientInfo);
    }

    this.sendResponse(client, id, {
      protocolVersion: '2024-11-05',
      capabilities: this.mcpService.getServerInfo().capabilities,
      serverInfo: {
        name: 'United Cellars Valuation API',
        version: '1.0.0',
      },
    });
  }

  private async handleToolCall(client: WebSocket, params: any, id: string | number) {
    const { name, arguments: args } = params;
    
    const result = await this.mcpService.executeTool({
      name,
      arguments: args || {},
    });

    this.sendResponse(client, id, result);
  }

  private sendMessage(client: WebSocket, message: McpMessage) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(message));
    }
  }

  private sendResponse(client: WebSocket, id: string | number, result: any) {
    this.sendMessage(client, {
      jsonrpc: '2.0',
      id,
      result,
    });
  }

  private sendError(
    client: WebSocket,
    code: number,
    message: string,
    id: string | number | null,
  ) {
    this.sendMessage(client, {
      jsonrpc: '2.0',
      id,
      error: {
        code,
        message,
      },
    });
  }

  private generateClientId(): string {
    return `mcp-client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Broadcast methods for real-time updates
  broadcastToolsUpdate() {
    const message: McpMessage = {
      jsonrpc: '2.0',
      method: 'notifications/tools/list_changed',
    };

    this.clients.forEach((clientInfo, client) => {
      if (clientInfo.authenticated) {
        this.sendMessage(client, message);
      }
    });
  }

  broadcastResourcesUpdate() {
    const message: McpMessage = {
      jsonrpc: '2.0',
      method: 'notifications/resources/list_changed',
    };

    this.clients.forEach((clientInfo, client) => {
      if (clientInfo.authenticated) {
        this.sendMessage(client, message);
      }
    });
  }

  broadcastPromptsUpdate() {
    const message: McpMessage = {
      jsonrpc: '2.0',
      method: 'notifications/prompts/list_changed',
    };

    this.clients.forEach((clientInfo, client) => {
      if (clientInfo.authenticated) {
        this.sendMessage(client, message);
      }
    });
  }
}
