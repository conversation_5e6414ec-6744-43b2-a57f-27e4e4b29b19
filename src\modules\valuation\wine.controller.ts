import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { WineService } from './wine.service';
import { CreateWineDto } from './dto/create-wine.dto';
import { UpdateWineDto } from './dto/update-wine.dto';
import { WineResponseDto, WinesListResponseDto } from './dto/wine-response.dto';
import { ErrorResponseDto } from '@/common/dto/api-response.dto';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/users/entities/user.entity';
import { WineType } from './entities/wine.entity';
import { Public } from '@/modules/auth/decorators/public.decorator';

@ApiTags('Wines')
@UseGuards(ThrottlerGuard)
@Controller({ path: 'wines', version: '1' })
export class WineController {
  constructor(private readonly wineService: WineService) { }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Add a new wine to the system',
    description: 'Create a new wine entry in the database. Authenticated users can add wines for valuation.'
  })
  @ApiBody({ type: CreateWineDto })
  @ApiResponse({
    status: 201,
    description: 'Wine created successfully',
    type: WineResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Wine already exists',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests',
    type: ErrorResponseDto,
  })
  create(@Body() createWineDto: CreateWineDto, @Request() req) {
    return this.wineService.create(createWineDto, req.user.id);
  }

  @Get()
  @Public()
  @ApiOperation({
    summary: 'Get all wines with pagination and filtering',
    description: 'Retrieve a paginated list of wines with optional search and filtering capabilities'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for wine name, producer, or region',
    example: 'Château Margaux',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: WineType,
    description: 'Filter by wine type',
    example: WineType.RED,
  })
  @ApiQuery({
    name: 'country',
    required: false,
    type: String,
    description: 'Filter by country of origin',
    example: 'France',
  })
  @ApiResponse({
    status: 200,
    description: 'Wines retrieved successfully',
    type: WinesListResponseDto,
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests',
    type: ErrorResponseDto,
  })
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('search') search?: string,
    @Query('type') type?: WineType,
    @Query('country') country?: string,
  ) {
    return this.wineService.findAll(page, limit, search, type, country);
  }

  @Get('search')
  @Public()
  @ApiOperation({ summary: 'Search wines by query' })
  @ApiQuery({ name: 'q', required: true, type: String, description: 'Search query' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  search(
    @Query('q') query: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return this.wineService.findAll(1, limit, query);
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get wine by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Wine retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Wine not found' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.wineService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.APPRAISER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update wine by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Wine updated successfully' })
  @ApiResponse({ status: 404, description: 'Wine not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateWineDto: UpdateWineDto,
    @Request() req,
  ) {
    return this.wineService.update(id, updateWineDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Delete wine by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Wine deleted successfully' })
  @ApiResponse({ status: 404, description: 'Wine not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  remove(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.wineService.remove(id, req.user.id);
  }
}
