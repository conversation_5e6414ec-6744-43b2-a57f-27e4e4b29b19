import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import { Public } from '@/modules/auth/decorators/public.decorator';

@ApiTags('Health')
@Controller({ path: 'health', version: '1' })
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get application health status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Health check successful',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        environment: { type: 'string' },
      },
    },
  })
  getHealth() {
    return this.healthService.getHealth();
  }

  @Public()
  @Get('detailed')
  @ApiOperation({ summary: 'Get detailed health information' })
  @ApiResponse({ 
    status: 200, 
    description: 'Detailed health information retrieved successfully',
  })
  getDetailedHealth() {
    return this.healthService.getDetailedHealth();
  }

  @Public()
  @Get('metrics')
  @ApiOperation({ summary: 'Get application metrics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Application metrics retrieved successfully',
  })
  getMetrics() {
    return this.healthService.getMetrics();
  }
}
