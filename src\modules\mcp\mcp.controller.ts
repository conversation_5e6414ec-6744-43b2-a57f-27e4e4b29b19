import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { McpService } from './mcp.service';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { Public } from '@/modules/auth/decorators/public.decorator';
import {
  McpServerInfoDto,
  McpToolsListDto,
  McpResourcesListDto,
  McpPromptsListDto,
  McpToolCallDto,
  McpToolResultDto,
} from './dto/mcp.dto';

@ApiTags('MCP (Model Context Protocol)')
@Controller({ path: 'mcp', version: '1' })
export class McpController {
  constructor(private readonly mcpService: McpService) {}

  @Get('info')
  @Public()
  @ApiOperation({
    summary: 'Get MCP server information',
    description: 'Returns server capabilities and metadata for MCP clients',
  })
  @ApiResponse({
    status: 200,
    description: 'MCP server information retrieved successfully',
    type: McpServerInfoDto,
  })
  getServerInfo() {
    return this.mcpService.getServerInfo();
  }

  @Get('tools')
  @Public()
  @ApiOperation({
    summary: 'List available MCP tools',
    description: 'Returns a list of all available tools that can be called via MCP',
  })
  @ApiResponse({
    status: 200,
    description: 'Available tools retrieved successfully',
    type: McpToolsListDto,
  })
  getTools() {
    return {
      tools: this.mcpService.getAvailableTools(),
    };
  }

  @Get('resources')
  @Public()
  @ApiOperation({
    summary: 'List available MCP resources',
    description: 'Returns a list of all available resources that can be accessed via MCP',
  })
  @ApiResponse({
    status: 200,
    description: 'Available resources retrieved successfully',
    type: McpResourcesListDto,
  })
  getResources() {
    return {
      resources: this.mcpService.getAvailableResources(),
    };
  }

  @Get('prompts')
  @Public()
  @ApiOperation({
    summary: 'List available MCP prompts',
    description: 'Returns a list of all available prompt templates for MCP clients',
  })
  @ApiResponse({
    status: 200,
    description: 'Available prompts retrieved successfully',
    type: McpPromptsListDto,
  })
  getPrompts() {
    return {
      prompts: this.mcpService.getAvailablePrompts(),
    };
  }

  @Post('tools/call')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Execute an MCP tool',
    description: 'Execute a specific tool with provided arguments. Requires authentication.',
  })
  @ApiBody({ type: McpToolCallDto })
  @ApiResponse({
    status: 200,
    description: 'Tool executed successfully',
    type: McpToolResultDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid tool call or arguments',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  async callTool(@Body() toolCall: McpToolCallDto, @Request() req) {
    return this.mcpService.executeTool(toolCall, req.user?.id);
  }

  @Post('tools/call/public')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Execute a public MCP tool',
    description: 'Execute tools that do not require authentication (read-only operations)',
  })
  @ApiBody({ type: McpToolCallDto })
  @ApiResponse({
    status: 200,
    description: 'Tool executed successfully',
    type: McpToolResultDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid tool call or arguments',
  })
  async callPublicTool(@Body() toolCall: McpToolCallDto) {
    // Only allow read-only tools for public access
    const readOnlyTools = ['search_wines', 'get_wine_details', 'get_user_stats'];
    
    if (!readOnlyTools.includes(toolCall.name)) {
      return {
        content: [
          {
            type: 'text',
            text: `Tool '${toolCall.name}' requires authentication`,
          },
        ],
        isError: true,
      };
    }

    return this.mcpService.executeTool(toolCall);
  }
}
