'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('valuation_history', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      valuation_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'valuations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      action: {
        type: Sequelize.ENUM('created', 'updated', 'status_changed', 'assigned', 'completed', 'cancelled', 'comment_added'),
        allowNull: false,
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      old_status: {
        type: Sequelize.ENUM('draft', 'pending', 'completed', 'cancelled'),
        allowNull: true,
      },
      new_status: {
        type: Sequelize.ENUM('draft', 'pending', 'completed', 'cancelled'),
        allowNull: true,
      },
      changes: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      comment: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add indexes
    await queryInterface.addIndex('valuation_history', ['valuation_id']);
    await queryInterface.addIndex('valuation_history', ['user_id']);
    await queryInterface.addIndex('valuation_history', ['action']);
    await queryInterface.addIndex('valuation_history', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('valuation_history');
  },
};
