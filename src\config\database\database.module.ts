import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from '@/modules/users/entities/user.entity';
import { Wine } from '@/modules/valuation/entities/wine.entity';
import { Valuation } from '@/modules/valuation/entities/valuation.entity';
import { ValuationHistory } from '@/modules/valuation/entities/valuation-history.entity';

@Module({
  imports: [
    SequelizeModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        dialect: 'postgres',
        host: configService.get<string>('DB_HOST'),
        port: configService.get<number>('DB_PORT'),
        username: configService.get<string>('DB_USERNAME'),
        password: configService.get<string>('DB_PASSWORD'),
        database: configService.get<string>('DB_DATABASE'),
        models: [User, Wine, Valuation, ValuationHistory],
        autoLoadModels: true,
        synchronize: configService.get<boolean>('DB_SYNC', false),
        logging: configService.get<boolean>('DB_LOGGING', false) 
          ? (sql: string) => console.log(`[SQL] ${sql}`)
          : false,
        pool: {
          max: 10,
          min: 0,
          acquire: 30000,
          idle: 10000,
        },
        define: {
          timestamps: true,
          underscored: true,
          paranoid: true, // Enable soft deletes
        },
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
