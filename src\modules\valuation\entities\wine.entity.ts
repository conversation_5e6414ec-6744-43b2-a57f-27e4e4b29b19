import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON>fault,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  HasMany,
} from 'sequelize-typescript';
import { Valuation } from './valuation.entity';

export enum WineType {
  RED = 'red',
  WHITE = 'white',
  ROSE = 'rose',
  SPARKLING = 'sparkling',
  DESSERT = 'dessert',
  FORTIFIED = 'fortified',
}

@Table({
  tableName: 'wines',
  timestamps: true,
  paranoid: true,
})
export class Wine extends Model<Wine> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  name: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  producer: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  region: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  country: string;

  @AllowNull(false)
  @Column(DataType.INTEGER)
  vintage: number;

  @AllowNull(false)
  @Column(DataType.ENUM(...Object.values(WineType)))
  type: WineType;

  @Column(DataType.STRING(255))
  appellation: string;

  @Column(DataType.STRING(255))
  classification: string;

  @Column(DataType.DECIMAL(3, 1))
  alcoholContent: number;

  @Column(DataType.STRING(50))
  bottleSize: string;

  @Column(DataType.TEXT)
  description: string;

  @Column(DataType.JSON)
  tastingNotes: Record<string, any>;

  @Column(DataType.JSON)
  ratings: Record<string, any>; // Store ratings from different sources

  @Column(DataType.DECIMAL(10, 2))
  averageRating: number;

  @Column(DataType.DECIMAL(12, 2))
  marketPrice: number;

  @Column(DataType.STRING(3))
  currency: string;

  @Column(DataType.JSON)
  metadata: Record<string, any>;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;

  @DeletedAt
  deletedAt: Date;

  // Associations
  @HasMany(() => Valuation)
  valuations: Valuation[];
}
