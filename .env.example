# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api
API_VERSION=v1

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=united_cellars_valuation
DB_SYNC=false
DB_LOGGING=true

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=http://localhost:3000

# Security
BCRYPT_ROUNDS=12
