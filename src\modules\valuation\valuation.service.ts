import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { Valuation, ValuationStatus } from './entities/valuation.entity';
import { Wine } from './entities/wine.entity';
import { ValuationHistory, HistoryAction } from './entities/valuation-history.entity';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { CreateValuationDto } from './dto/create-valuation.dto';
import { UpdateValuationDto } from './dto/update-valuation.dto';
import { LoggerService } from '@/common/services/logger.service';

@Injectable()
export class ValuationService {
  constructor(
    @InjectModel(Valuation)
    private valuationModel: typeof Valuation,
    @InjectModel(Wine)
    private wineModel: typeof Wine,
    @InjectModel(ValuationHistory)
    private historyModel: typeof ValuationHistory,
    private logger: LoggerService,
  ) { }

  async create(createValuationDto: CreateValuationDto, userId: string): Promise<Valuation> {
    // Generate reference number
    const referenceNumber = await this.generateReferenceNumber();

    const valuation = await this.valuationModel.create({
      ...createValuationDto,
      userId,
      referenceNumber,
      status: ValuationStatus.DRAFT,
      valuationDate: createValuationDto.valuationDate ? new Date(createValuationDto.valuationDate) : null,
    });

    // Create history entry
    await this.createHistoryEntry(
      valuation.id,
      userId,
      HistoryAction.CREATED,
      'Valuation request created',
    );

    this.logger.log(`Valuation created: ${referenceNumber}`, 'ValuationService');
    return valuation;
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    userId?: string,
    userRole?: UserRole,
    status?: ValuationStatus,
  ) {
    const offset = (page - 1) * limit;
    const where: any = {};

    // Apply user-based filtering
    if (userRole === UserRole.USER) {
      where.userId = userId;
    } else if (userRole === UserRole.APPRAISER) {
      where[Op.or] = [
        { appraiserId: userId },
        { status: { [Op.in]: [ValuationStatus.PENDING] } },
      ];
    }

    if (status) {
      where.status = status;
    }

    const { rows: valuations, count: total } = await this.valuationModel.findAndCountAll({
      where,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      include: [
        { model: Wine, as: 'wine' },
        { model: User, as: 'user', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: User, as: 'appraiser', attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
    });

    return {
      valuations,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, userId: string, userRole: UserRole): Promise<Valuation> {
    const valuation = await this.valuationModel.findByPk(id, {
      include: [
        { model: Wine, as: 'wine' },
        { model: User, as: 'user', attributes: ['id', 'firstName', 'lastName', 'email'] },
        { model: User, as: 'appraiser', attributes: ['id', 'firstName', 'lastName', 'email'] },
        {
          model: ValuationHistory,
          as: 'history',
          include: [{ model: User, attributes: ['id', 'firstName', 'lastName'] }],
          order: [['createdAt', 'DESC']],
        },
      ],
    });

    if (!valuation) {
      throw new NotFoundException('Valuation not found');
    }

    // Check access permissions
    if (userRole === UserRole.USER && valuation.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return valuation;
  }

  async update(id: string, updateValuationDto: UpdateValuationDto, userId: string, userRole: UserRole): Promise<Valuation> {
    const valuation = await this.findOne(id, userId, userRole);

    // Check if user can update
    if (userRole === UserRole.USER && valuation.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    const oldValues = { ...valuation.toJSON() };

    // Convert string dates to Date objects
    const updateData = {
      ...updateValuationDto,
      ...(updateValuationDto.valuationDate && {
        valuationDate: new Date(updateValuationDto.valuationDate)
      }),
    };

    await valuation.update(updateData);

    // Create history entry
    await this.createHistoryEntry(
      valuation.id,
      userId,
      HistoryAction.UPDATED,
      'Valuation updated',
      this.getChanges(oldValues, valuation.toJSON()),
    );

    this.logger.log(`Valuation updated: ${valuation.referenceNumber}`, 'ValuationService');
    return valuation;
  }

  async updateStatus(
    id: string,
    newStatus: ValuationStatus,
    userId: string,
    userRole: UserRole,
    comment?: string,
  ): Promise<Valuation> {
    const valuation = await this.findOne(id, userId, userRole);
    const oldStatus = valuation.status;

    // Validate status transition
    this.validateStatusTransition(oldStatus, newStatus, userRole);

    await valuation.update({
      status: newStatus,
      ...(newStatus === ValuationStatus.COMPLETED && { completedAt: new Date() }),
    });

    // Create history entry
    await this.createHistoryEntry(
      valuation.id,
      userId,
      HistoryAction.STATUS_CHANGED,
      `Status changed from ${oldStatus} to ${newStatus}`,
      { oldStatus, newStatus },
      comment,
    );

    this.logger.log(
      `Valuation status changed: ${valuation.referenceNumber} - ${oldStatus} to ${newStatus}`,
      'ValuationService',
    );

    return valuation;
  }

  async assignAppraiser(id: string, appraiserId: string, userId: string): Promise<Valuation> {
    const valuation = await this.valuationModel.findByPk(id);

    if (!valuation) {
      throw new NotFoundException('Valuation not found');
    }

    await valuation.update({ appraiserId });

    // Create history entry
    await this.createHistoryEntry(
      valuation.id,
      userId,
      HistoryAction.ASSIGNED,
      `Assigned to appraiser`,
      { appraiserId },
    );

    this.logger.log(`Valuation assigned: ${valuation.referenceNumber}`, 'ValuationService');
    return valuation;
  }

  async remove(id: string, userId: string, userRole: UserRole): Promise<void> {
    const valuation = await this.findOne(id, userId, userRole);

    if (userRole === UserRole.USER && valuation.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    await valuation.destroy();
    this.logger.log(`Valuation deleted: ${valuation.referenceNumber}`, 'ValuationService');
  }

  private async generateReferenceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const count = await this.valuationModel.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(`${year}-01-01`),
          [Op.lt]: new Date(`${year + 1}-01-01`),
        },
      },
    });

    return `VAL-${year}-${String(count + 1).padStart(6, '0')}`;
  }

  private async createHistoryEntry(
    valuationId: string,
    userId: string,
    action: HistoryAction,
    description: string,
    changes?: Record<string, any>,
    comment?: string,
  ): Promise<void> {
    await this.historyModel.create({
      valuationId,
      userId,
      action,
      description,
      changes,
      comment,
    });
  }

  private getChanges(oldValues: any, newValues: any): Record<string, any> {
    const changes: Record<string, any> = {};

    for (const key in newValues) {
      if (oldValues[key] !== newValues[key] && key !== 'updatedAt') {
        changes[key] = {
          from: oldValues[key],
          to: newValues[key],
        };
      }
    }

    return changes;
  }

  private validateStatusTransition(
    oldStatus: ValuationStatus,
    newStatus: ValuationStatus,
    userRole: UserRole,
  ): void {
    const allowedTransitions: Record<ValuationStatus, ValuationStatus[]> = {
      [ValuationStatus.DRAFT]: [ValuationStatus.PENDING, ValuationStatus.CANCELLED],
      [ValuationStatus.PENDING]: [ValuationStatus.COMPLETED, ValuationStatus.CANCELLED],
      [ValuationStatus.COMPLETED]: [],
      [ValuationStatus.CANCELLED]: [ValuationStatus.DRAFT, ValuationStatus.PENDING],
    };

    if (!allowedTransitions[oldStatus].includes(newStatus)) {
      throw new ForbiddenException(`Cannot transition from ${oldStatus} to ${newStatus}`);
    }

    // Additional role-based validations
    if (newStatus === ValuationStatus.COMPLETED && userRole !== UserRole.APPRAISER && userRole !== UserRole.ADMIN) {
      throw new ForbiddenException('Only appraisers can complete valuations');
    }
  }
}
