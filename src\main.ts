import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { LoggerService } from '@/common/services/logger.service';
import { MetricsService } from '@/common/services/metrics.service';
import { HttpExceptionFilter } from '@/common/filters/http-exception.filter';
import { ResponseInterceptor } from '@/common/interceptors/response.interceptor';
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  const configService = app.get(ConfigService);
  const loggerService = app.get(LoggerService);
  const metricsService = app.get(MetricsService);

  app.useLogger(loggerService);

  // Security
  app.use(helmet());

  // CORS
  if (configService.get<boolean>('CORS_ENABLED')) {
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN'),
      credentials: true,
    });
  }

  // Global prefix and versioning
  const apiPrefix = configService.get<string>('API_PREFIX', 'api');
  app.setGlobalPrefix(apiPrefix);

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: configService.get<string>('API_VERSION', 'v1'),
  });

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global filters and interceptors
  app.useGlobalFilters(new HttpExceptionFilter(loggerService));
  app.useGlobalInterceptors(new ResponseInterceptor(app.get(MetricsService)));

  // Swagger documentation
  const enableSwagger = configService.get<boolean>('SWAGGER_ENABLED', configService.get<string>('NODE_ENV') !== 'production');
  if (enableSwagger) {
    const config = new DocumentBuilder()
      .setTitle('United Cellars Valuation API')
      .setDescription('API for United Cellars wine valuation tool')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      );

    // Force HTTP protocol in production if specified
    const forceHttp = configService.get<boolean>('SWAGGER_FORCE_HTTP', false);
    if (forceHttp) {
      const port = configService.get<number>('PORT', 3000);
      config.addServer(`http://***********:${port}`, 'Production HTTP Server');
      config.addServer(`http://localhost:${port}`, 'Local HTTP Server');
    }

    const swaggerConfig = config.build();
    const document = SwaggerModule.createDocument(app, swaggerConfig);

    const swaggerOptions: any = {
      persistAuthorization: true,
    };

    // Force HTTP scheme when SWAGGER_FORCE_HTTP is enabled
    if (forceHttp) {
      swaggerOptions.defaultModelsExpandDepth = -1;
      swaggerOptions.docExpansion = 'none';
      // Override the servers in the document to ensure HTTP is used
      document.servers = [
        { url: `http://***********:${configService.get<number>('PORT', 3000)}`, description: 'Production HTTP Server' },
        { url: `http://localhost:${configService.get<number>('PORT', 3000)}`, description: 'Local HTTP Server' }
      ];
    }

    SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
      swaggerOptions,
    });
  }

  const port = configService.get<number>('PORT', 3000);
  await app.listen(port);

  loggerService.log(`🚀 Application is running on: http://localhost:${port}/${apiPrefix}`);

  if (enableSwagger) {
    loggerService.log(`📚 Swagger documentation: http://localhost:${port}/${apiPrefix}/docs`);
  }

  loggerService.log(`🔌 MCP server endpoints:`);
  loggerService.log(`   HTTP: http://localhost:${port}/${apiPrefix}/mcp`);
  loggerService.log(`   WebSocket: ws://localhost:3001/mcp`);
}

bootstrap().catch((error) => {
  console.error('❌ Error starting server:', error);
  process.exit(1);
});
