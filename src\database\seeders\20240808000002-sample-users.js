'use strict';

const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('Password123!', 12);
    
    await queryInterface.bulkInsert('users', [
      {
        id: uuidv4(),
        first_name: '<PERSON>',
        last_name: 'Appraiser',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567890',
        role: 'appraiser',
        status: 'active',
        email_verified_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: uuidv4(),
        first_name: '<PERSON>',
        last_name: 'Customer',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567891',
        role: 'user',
        status: 'active',
        email_verified_at: new Date(),
        preferences: JSON.stringify({
          theme: 'light',
          notifications: true,
          currency: 'USD',
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: uuidv4(),
        first_name: '<PERSON>',
        last_name: 'Collector',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567892',
        role: 'user',
        status: 'active',
        email_verified_at: new Date(),
        preferences: JSON.stringify({
          theme: 'dark',
          notifications: false,
          currency: 'EUR',
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', {
      email: {
        [Sequelize.Op.in]: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ],
      },
    });
  },
};
