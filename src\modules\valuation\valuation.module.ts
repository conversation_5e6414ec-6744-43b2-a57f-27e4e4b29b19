import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ValuationService } from './valuation.service';
import { ValuationController } from './valuation.controller';
import { WineService } from './wine.service';
import { WineController } from './wine.controller';
import { Valuation } from './entities/valuation.entity';
import { Wine } from './entities/wine.entity';
import { ValuationHistory } from './entities/valuation-history.entity';

@Module({
  imports: [SequelizeModule.forFeature([Valuation, Wine, ValuationHistory])],
  controllers: [ValuationController, WineController],
  providers: [ValuationService, WineService],
  exports: [ValuationService, WineService],
})
export class ValuationModule { }
