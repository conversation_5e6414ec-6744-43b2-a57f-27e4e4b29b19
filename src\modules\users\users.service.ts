import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { User, UserRole, UserStatus } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { LoggerService } from '@/common/services/logger.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    private logger: LoggerService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    try {
      // Check if user already exists
      const existingUser = await this.userModel.findOne({
        where: { email: createUserDto.email },
        paranoid: false, // Include soft-deleted records
      });

      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }

      const user = await this.userModel.create(createUserDto);
      this.logger.log(`User created: ${user.email}`, 'UsersService');
      
      return user;
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, error.stack, 'UsersService');
      throw error;
    }
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    role?: UserRole,
    status?: UserStatus,
  ) {
    const offset = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (status) {
      where.status = status;
    }

    const { rows: users, count: total } = await this.userModel.findAndCountAll({
      where,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      attributes: { exclude: ['password'] },
    });

    return {
      users,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userModel.findByPk(id, {
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({
      where: { email },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // Check email uniqueness if email is being updated
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userModel.findOne({
        where: { 
          email: updateUserDto.email,
          id: { [Op.ne]: id },
        },
        paranoid: false,
      });

      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }
    }

    await user.update(updateUserDto);
    this.logger.log(`User updated: ${user.email}`, 'UsersService');
    
    return user;
  }

  async changePassword(id: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const user = await this.userModel.findByPk(id);
    
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const isCurrentPasswordValid = await user.validatePassword(changePasswordDto.currentPassword);
    
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    await user.update({ password: changePasswordDto.newPassword });
    this.logger.log(`Password changed for user: ${user.email}`, 'UsersService');
  }

  async updateLastLogin(id: string, ip: string): Promise<void> {
    await this.userModel.update(
      { 
        lastLoginAt: new Date(),
        lastLoginIp: ip,
      },
      { where: { id } },
    );
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await user.destroy();
    this.logger.log(`User soft deleted: ${user.email}`, 'UsersService');
  }

  async restore(id: string): Promise<User> {
    const user = await this.userModel.findByPk(id, { paranoid: false });
    
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.deletedAt) {
      throw new BadRequestException('User is not deleted');
    }

    await user.restore();
    this.logger.log(`User restored: ${user.email}`, 'UsersService');
    
    return user;
  }

  async getUserStats() {
    const [total, active, inactive, suspended, admins, users, appraisers] = await Promise.all([
      this.userModel.count(),
      this.userModel.count({ where: { status: UserStatus.ACTIVE } }),
      this.userModel.count({ where: { status: UserStatus.INACTIVE } }),
      this.userModel.count({ where: { status: UserStatus.SUSPENDED } }),
      this.userModel.count({ where: { role: UserRole.ADMIN } }),
      this.userModel.count({ where: { role: UserRole.USER } }),
      this.userModel.count({ where: { role: UserRole.APPRAISER } }),
    ]);

    return {
      total,
      byStatus: {
        active,
        inactive,
        suspended,
      },
      byRole: {
        admins,
        users,
        appraisers,
      },
    };
  }
}
