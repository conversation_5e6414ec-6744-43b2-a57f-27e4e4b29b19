import { Injectable } from '@nestjs/common';
import { LoggerService } from '@/common/services/logger.service';
import { UsersService } from '@/modules/users/users.service';
import { WineService } from '@/modules/valuation/wine.service';
import { ValuationService } from '@/modules/valuation/valuation.service';
import {
  McpTool,
  McpResource,
  McpPrompt,
  McpToolCall,
  McpToolResult,
  McpServerInfo,
} from './interfaces/mcp.interface';

@Injectable()
export class McpService {
  constructor(
    private logger: LoggerService,
    private usersService: UsersService,
    private wineService: WineService,
    private valuationService: ValuationService,
  ) {}

  getServerInfo(): McpServerInfo {
    return {
      name: 'United Cellars Valuation API',
      version: '1.0.0',
      capabilities: {
        logging: {},
        prompts: {
          listChanged: true,
        },
        resources: {
          subscribe: true,
          listChanged: true,
        },
        tools: {
          listChanged: true,
        },
      },
    };
  }

  getAvailableTools(): McpTool[] {
    return [
      {
        name: 'search_wines',
        description: 'Search for wines in the database by name, producer, region, or other criteria',
        inputSchema: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'Search query for wine name, producer, or region',
            },
            type: {
              type: 'string',
              enum: ['red', 'white', 'rose', 'sparkling', 'dessert', 'fortified'],
              description: 'Filter by wine type',
            },
            country: {
              type: 'string',
              description: 'Filter by country of origin',
            },
            limit: {
              type: 'number',
              description: 'Maximum number of results to return (default: 10)',
              minimum: 1,
              maximum: 100,
            },
          },
          required: [],
        },
      },
      {
        name: 'get_wine_details',
        description: 'Get detailed information about a specific wine by ID',
        inputSchema: {
          type: 'object',
          properties: {
            wineId: {
              type: 'string',
              description: 'UUID of the wine to retrieve',
            },
          },
          required: ['wineId'],
        },
      },
      {
        name: 'get_valuations',
        description: 'Get valuation requests with optional filtering',
        inputSchema: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['draft', 'pending', 'completed', 'cancelled'],
              description: 'Filter by valuation status',
            },
            userId: {
              type: 'string',
              description: 'Filter by user ID (admin/appraiser only)',
            },
            limit: {
              type: 'number',
              description: 'Maximum number of results to return (default: 10)',
              minimum: 1,
              maximum: 100,
            },
          },
          required: [],
        },
      },
      {
        name: 'get_valuation_details',
        description: 'Get detailed information about a specific valuation by ID',
        inputSchema: {
          type: 'object',
          properties: {
            valuationId: {
              type: 'string',
              description: 'UUID of the valuation to retrieve',
            },
          },
          required: ['valuationId'],
        },
      },
      {
        name: 'get_user_stats',
        description: 'Get user statistics and analytics (admin only)',
        inputSchema: {
          type: 'object',
          properties: {},
          required: [],
        },
      },
      {
        name: 'create_wine',
        description: 'Add a new wine to the database',
        inputSchema: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: 'Wine name',
            },
            producer: {
              type: 'string',
              description: 'Wine producer/winery',
            },
            region: {
              type: 'string',
              description: 'Wine region',
            },
            country: {
              type: 'string',
              description: 'Country of origin',
            },
            vintage: {
              type: 'number',
              description: 'Wine vintage year',
              minimum: 1800,
              maximum: new Date().getFullYear(),
            },
            type: {
              type: 'string',
              enum: ['red', 'white', 'rose', 'sparkling', 'dessert', 'fortified'],
              description: 'Type of wine',
            },
            appellation: {
              type: 'string',
              description: 'Wine appellation (optional)',
            },
            classification: {
              type: 'string',
              description: 'Wine classification (optional)',
            },
            alcoholContent: {
              type: 'number',
              description: 'Alcohol content percentage (optional)',
              minimum: 0,
              maximum: 50,
            },
            description: {
              type: 'string',
              description: 'Wine description (optional)',
            },
          },
          required: ['name', 'producer', 'region', 'country', 'vintage', 'type'],
        },
      },
    ];
  }

  getAvailableResources(): McpResource[] {
    return [
      {
        uri: 'wines://database',
        name: 'Wine Database',
        description: 'Access to the complete wine database with search capabilities',
        mimeType: 'application/json',
      },
      {
        uri: 'valuations://database',
        name: 'Valuation Database',
        description: 'Access to wine valuation requests and history',
        mimeType: 'application/json',
      },
      {
        uri: 'users://database',
        name: 'User Database',
        description: 'Access to user information and statistics',
        mimeType: 'application/json',
      },
      {
        uri: 'api://documentation',
        name: 'API Documentation',
        description: 'Complete API documentation and usage examples',
        mimeType: 'text/markdown',
      },
    ];
  }

  getAvailablePrompts(): McpPrompt[] {
    return [
      {
        name: 'wine_recommendation',
        description: 'Get wine recommendations based on preferences and criteria',
        arguments: [
          {
            name: 'preferences',
            description: 'User preferences (type, region, price range, etc.)',
            required: true,
          },
          {
            name: 'occasion',
            description: 'Occasion or purpose for the wine',
            required: false,
          },
        ],
      },
      {
        name: 'valuation_analysis',
        description: 'Analyze wine valuation data and provide insights',
        arguments: [
          {
            name: 'wineId',
            description: 'Wine ID to analyze',
            required: true,
          },
          {
            name: 'purpose',
            description: 'Purpose of the valuation analysis',
            required: false,
          },
        ],
      },
      {
        name: 'market_trends',
        description: 'Analyze market trends for specific wine categories',
        arguments: [
          {
            name: 'category',
            description: 'Wine category to analyze (type, region, producer)',
            required: true,
          },
          {
            name: 'timeframe',
            description: 'Time frame for the analysis',
            required: false,
          },
        ],
      },
    ];
  }

  async executeTool(toolCall: McpToolCall, userId?: string): Promise<McpToolResult> {
    try {
      this.logger.log(`Executing MCP tool: ${toolCall.name}`, 'McpService');

      switch (toolCall.name) {
        case 'search_wines':
          return await this.searchWines(toolCall.arguments);

        case 'get_wine_details':
          return await this.getWineDetails(toolCall.arguments.wineId);

        case 'get_valuations':
          return await this.getValuations(toolCall.arguments, userId);

        case 'get_valuation_details':
          return await this.getValuationDetails(toolCall.arguments.valuationId, userId);

        case 'get_user_stats':
          return await this.getUserStats();

        case 'create_wine':
          return await this.createWine(toolCall.arguments, userId);

        default:
          return {
            content: [
              {
                type: 'text',
                text: `Unknown tool: ${toolCall.name}`,
              },
            ],
            isError: true,
          };
      }
    } catch (error) {
      this.logger.error(`MCP tool execution failed: ${error.message}`, error.stack, 'McpService');
      return {
        content: [
          {
            type: 'text',
            text: `Tool execution failed: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async searchWines(args: any): Promise<McpToolResult> {
    const { query, type, country, limit = 10 } = args;
    const result = await this.wineService.findAll(1, limit, query, type, country);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            wines: result.wines,
            pagination: result.pagination,
            totalFound: result.pagination.total,
          }, null, 2),
        },
      ],
    };
  }

  private async getWineDetails(wineId: string): Promise<McpToolResult> {
    const wine = await this.wineService.findOne(wineId);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(wine, null, 2),
        },
      ],
    };
  }

  private async getValuations(args: any, userId?: string): Promise<McpToolResult> {
    const { status, limit = 10 } = args;
    // Note: This would need proper role checking in a real implementation
    const result = await this.valuationService.findAll(1, limit, userId, 'admin' as any, status);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            valuations: result.valuations,
            pagination: result.pagination,
          }, null, 2),
        },
      ],
    };
  }

  private async getValuationDetails(valuationId: string, userId?: string): Promise<McpToolResult> {
    // Note: This would need proper role checking in a real implementation
    const valuation = await this.valuationService.findOne(valuationId, userId || '', 'admin' as any);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(valuation, null, 2),
        },
      ],
    };
  }

  private async getUserStats(): Promise<McpToolResult> {
    const stats = await this.usersService.getUserStats();

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(stats, null, 2),
        },
      ],
    };
  }

  private async createWine(args: any, userId?: string): Promise<McpToolResult> {
    if (!userId) {
      return {
        content: [
          {
            type: 'text',
            text: 'Authentication required to create wines',
          },
        ],
        isError: true,
      };
    }

    const wine = await this.wineService.create(args, userId);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            message: 'Wine created successfully',
            wine,
          }, null, 2),
        },
      ],
    };
  }
}
