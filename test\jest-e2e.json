{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapping": {"^@/(.*)$": "<rootDir>/../src/$1"}, "setupFilesAfterEnv": ["<rootDir>/setup.ts"], "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/main.ts", "!src/**/*.spec.ts", "!src/**/*.interface.ts", "!src/**/*.dto.ts"], "coverageDirectory": "../coverage-e2e"}