import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponseDto } from '@/common/dto/api-response.dto';
import { WineResponseDto } from './wine-response.dto';
import { UserResponseDto } from '@/modules/users/dto/user-response.dto';

export class ValuationHistoryDto {
  @ApiProperty({
    description: 'History entry ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Action performed',
    example: 'status_changed',
    enum: ['created', 'updated', 'status_changed', 'assigned', 'completed', 'cancelled', 'comment_added'],
  })
  action: string;

  @ApiProperty({
    description: 'Action description',
    example: 'Status changed from draft to pending',
  })
  description: string;

  @ApiProperty({
    description: 'Previous status',
    example: 'draft',
    required: false,
  })
  oldStatus?: string;

  @ApiProperty({
    description: 'New status',
    example: 'pending',
    required: false,
  })
  newStatus?: string;

  @ApiProperty({
    description: 'Changes made',
    example: { status: { from: 'draft', to: 'pending' } },
    required: false,
  })
  changes?: Record<string, any>;

  @ApiProperty({
    description: 'Comment added',
    example: 'Ready for appraisal',
    required: false,
  })
  comment?: string;

  @ApiProperty({
    description: 'User who performed the action',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Timestamp of the action',
    example: '2024-08-08T10:30:00.000Z',
  })
  createdAt: string;
}

export class ValuationResponseDto {
  @ApiProperty({
    description: 'Valuation ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Reference number',
    example: 'VAL-2024-000001',
  })
  referenceNumber: string;

  @ApiProperty({
    description: 'Valuation status',
    example: 'pending',
    enum: ['draft', 'pending', 'completed', 'cancelled'],
  })
  status: string;

  @ApiProperty({
    description: 'Purpose of valuation',
    example: 'insurance',
    enum: ['insurance', 'sale', 'purchase', 'estate', 'donation', 'other'],
  })
  purpose: string;

  @ApiProperty({
    description: 'Quantity of bottles',
    example: 1,
  })
  quantity: number;

  @ApiProperty({
    description: 'Condition of the wine',
    example: 'Excellent - stored in professional cellar',
    required: false,
  })
  condition?: string;

  @ApiProperty({
    description: 'Provenance information',
    example: 'Purchased directly from château in 2012',
    required: false,
  })
  provenance?: string;

  @ApiProperty({
    description: 'Storage conditions',
    example: 'Temperature controlled cellar, 55°F, 70% humidity',
    required: false,
  })
  storage?: string;

  @ApiProperty({
    description: 'Estimated value',
    example: 850.00,
    required: false,
  })
  estimatedValue?: number;

  @ApiProperty({
    description: 'Final appraised value',
    example: 920.00,
    required: false,
  })
  finalValue?: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    required: false,
  })
  currency?: string;

  @ApiProperty({
    description: 'Valuation date',
    example: '2024-08-08T10:30:00.000Z',
    required: false,
  })
  valuationDate?: string;

  @ApiProperty({
    description: 'Expiry date',
    example: '2025-08-08T10:30:00.000Z',
    required: false,
  })
  expiryDate?: string;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Wine has been in collection for 10 years...',
    required: false,
  })
  notes?: string;

  @ApiProperty({
    description: 'Valuation methodology',
    example: { approach: 'market_comparison', factors: ['condition', 'provenance', 'rarity'] },
    required: false,
  })
  methodology?: Record<string, any>;

  @ApiProperty({
    description: 'Market data used',
    example: { recent_sales: [850, 900, 875], average: 875 },
    required: false,
  })
  marketData?: Record<string, any>;

  @ApiProperty({
    description: 'Wine being valued',
    type: WineResponseDto,
  })
  wine: WineResponseDto;

  @ApiProperty({
    description: 'User who requested the valuation',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    description: 'Assigned appraiser',
    type: UserResponseDto,
    required: false,
  })
  appraiser?: UserResponseDto;

  @ApiProperty({
    description: 'Valuation history',
    type: [ValuationHistoryDto],
    required: false,
  })
  history?: ValuationHistoryDto[];

  @ApiProperty({
    description: 'Completion timestamp',
    example: '2024-08-08T10:30:00.000Z',
    required: false,
  })
  completedAt?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-08-08T10:30:00.000Z',
  })
  updatedAt: string;
}

export class ValuationsListResponseDto extends PaginatedResponseDto<ValuationResponseDto> {
  @ApiProperty({
    description: 'Array of valuations',
    type: [ValuationResponseDto],
  })
  valuations: ValuationResponseDto[];
}
