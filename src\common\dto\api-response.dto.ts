import { ApiProperty } from '@nestjs/swagger';

export class ApiResponseDto<T> {
  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({ description: 'HTTP status code', example: 200 })
  statusCode: number;

  @ApiProperty({ description: 'Response timestamp', example: '2024-08-08T10:30:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: 'Request path', example: '/api/v1/users' })
  path: string;

  @ApiProperty({ description: 'HTTP method', example: 'GET' })
  method: string;

  @ApiProperty({ description: 'Response data' })
  data: T;

  @ApiProperty({ description: 'Response message', required: false })
  message?: string;
}

export class PaginationDto {
  @ApiProperty({ description: 'Total number of items', example: 100 })
  total: number;

  @ApiProperty({ description: 'Current page number', example: 1 })
  page: number;

  @ApiProperty({ description: 'Items per page', example: 10 })
  limit: number;

  @ApiProperty({ description: 'Total number of pages', example: 10 })
  totalPages: number;
}

export class PaginatedResponseDto<T> {
  @ApiProperty({ description: 'Array of items' })
  items: T[];

  @ApiProperty({ description: 'Pagination information' })
  pagination: PaginationDto;
}

export class ErrorResponseDto {
  @ApiProperty({ description: 'Success status', example: false })
  success: boolean;

  @ApiProperty({ description: 'HTTP status code', example: 400 })
  statusCode: number;

  @ApiProperty({ description: 'Error timestamp', example: '2024-08-08T10:30:00.000Z' })
  timestamp: string;

  @ApiProperty({ description: 'Request path', example: '/api/v1/users' })
  path: string;

  @ApiProperty({ description: 'HTTP method', example: 'POST' })
  method: string;

  @ApiProperty({ description: 'Error message', example: 'Validation failed' })
  message: string;

  @ApiProperty({ description: 'Detailed error information', required: false })
  errors?: any;
}
