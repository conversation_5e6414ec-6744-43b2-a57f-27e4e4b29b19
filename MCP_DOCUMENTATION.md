# United Cellars MCP Server Documentation

## 🔌 Model Context Protocol (MCP) Integration

The United Cellars API includes a comprehensive MCP server implementation that allows AI assistants and other MCP clients to interact with the wine valuation system programmatically.

## 🚀 Quick Start

### 1. Server Endpoints
- **HTTP API**: `http://localhost:3000/api/v1/mcp`
- **WebSocket**: `ws://localhost:3001/mcp`
- **Swagger Docs**: `http://localhost:3000/api/docs#/MCP%20(Model%20Context%20Protocol)`

### 2. Basic Usage

#### Get Server Information
```bash
curl -X GET "http://localhost:3000/api/v1/mcp/info"
```

#### List Available Tools
```bash
curl -X GET "http://localhost:3000/api/v1/mcp/tools"
```

#### Execute a Tool (Public)
```bash
curl -X POST "http://localhost:3000/api/v1/mcp/tools/call/public" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "search_wines",
    "arguments": {
      "query": "Bordeaux",
      "type": "red",
      "limit": 5
    }
  }'
```

## 🛠️ Available Tools

### 1. search_wines
Search for wines in the database by various criteria.

**Parameters:**
- `query` (string, optional): Search term for wine name, producer, or region
- `type` (string, optional): Wine type (red, white, rose, sparkling, dessert, fortified)
- `country` (string, optional): Filter by country of origin
- `limit` (number, optional): Maximum results (1-100, default: 10)

**Example:**
```json
{
  "name": "search_wines",
  "arguments": {
    "query": "Château Margaux",
    "type": "red",
    "country": "France",
    "limit": 5
  }
}
```

### 2. get_wine_details
Get detailed information about a specific wine.

**Parameters:**
- `wineId` (string, required): UUID of the wine

**Example:**
```json
{
  "name": "get_wine_details",
  "arguments": {
    "wineId": "123e4567-e89b-12d3-a456-************"
  }
}
```

### 3. get_valuations
Get valuation requests with optional filtering.

**Parameters:**
- `status` (string, optional): Filter by status (draft, pending, completed, cancelled)
- `userId` (string, optional): Filter by user ID (admin/appraiser only)
- `limit` (number, optional): Maximum results (1-100, default: 10)

**Example:**
```json
{
  "name": "get_valuations",
  "arguments": {
    "status": "pending",
    "limit": 10
  }
}
```

### 4. get_valuation_details
Get detailed information about a specific valuation.

**Parameters:**
- `valuationId` (string, required): UUID of the valuation

**Example:**
```json
{
  "name": "get_valuation_details",
  "arguments": {
    "valuationId": "123e4567-e89b-12d3-a456-************"
  }
}
```

### 5. get_user_stats
Get user statistics and analytics (admin only).

**Parameters:** None

**Example:**
```json
{
  "name": "get_user_stats",
  "arguments": {}
}
```

### 6. create_wine
Add a new wine to the database (requires authentication).

**Parameters:**
- `name` (string, required): Wine name
- `producer` (string, required): Wine producer/winery
- `region` (string, required): Wine region
- `country` (string, required): Country of origin
- `vintage` (number, required): Wine vintage year (1800-current year)
- `type` (string, required): Wine type
- `appellation` (string, optional): Wine appellation
- `classification` (string, optional): Wine classification
- `alcoholContent` (number, optional): Alcohol content percentage (0-50)
- `description` (string, optional): Wine description

**Example:**
```json
{
  "name": "create_wine",
  "arguments": {
    "name": "Château Pichon Baron",
    "producer": "Château Pichon Baron",
    "region": "Pauillac",
    "country": "France",
    "vintage": 2015,
    "type": "red",
    "appellation": "Pauillac AOC",
    "classification": "Deuxième Grand Cru Classé"
  }
}
```

## 📚 Available Resources

### 1. wines://database
Access to the complete wine database with search capabilities.

### 2. valuations://database
Access to wine valuation requests and history.

### 3. users://database
Access to user information and statistics.

### 4. api://documentation
Complete API documentation and usage examples.

## 🎯 Available Prompts

### 1. wine_recommendation
Get wine recommendations based on preferences and criteria.

**Arguments:**
- `preferences` (required): User preferences (type, region, price range, etc.)
- `occasion` (optional): Occasion or purpose for the wine

### 2. valuation_analysis
Analyze wine valuation data and provide insights.

**Arguments:**
- `wineId` (required): Wine ID to analyze
- `purpose` (optional): Purpose of the valuation analysis

### 3. market_trends
Analyze market trends for specific wine categories.

**Arguments:**
- `category` (required): Wine category to analyze (type, region, producer)
- `timeframe` (optional): Time frame for the analysis

## 🔗 WebSocket Integration

### Connection
```javascript
const ws = new WebSocket('ws://localhost:3001/mcp');

ws.on('open', () => {
  console.log('Connected to MCP server');
  
  // Initialize connection
  ws.send(JSON.stringify({
    jsonrpc: '2.0',
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: { listChanged: true },
        resources: { subscribe: true },
        prompts: { listChanged: true }
      }
    },
    id: 1
  }));
});
```

### Calling Tools
```javascript
// Search for wines
ws.send(JSON.stringify({
  jsonrpc: '2.0',
  method: 'tools/call',
  params: {
    name: 'search_wines',
    arguments: {
      query: 'Bordeaux',
      type: 'red',
      limit: 5
    }
  },
  id: 2
}));

// Handle response
ws.on('message', (data) => {
  const message = JSON.parse(data.toString());
  if (message.id === 2) {
    console.log('Search results:', message.result);
  }
});
```

### Real-time Notifications
The server sends notifications when tools, resources, or prompts are updated:

```javascript
ws.on('message', (data) => {
  const message = JSON.parse(data.toString());
  
  switch (message.method) {
    case 'notifications/tools/list_changed':
      console.log('Tools list updated');
      break;
    case 'notifications/resources/list_changed':
      console.log('Resources list updated');
      break;
    case 'notifications/prompts/list_changed':
      console.log('Prompts list updated');
      break;
  }
});
```

## 🤖 AI Assistant Integration

### Claude Desktop Integration

1. Create or edit your Claude Desktop MCP configuration file:

**Windows:** `%APPDATA%\Claude\claude_desktop_config.json`
**macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`

2. Add the United Cellars MCP server:

```json
{
  "mcpServers": {
    "united-cellars": {
      "command": "node",
      "args": ["C:/path/to/united-cellars/dist/main.js"],
      "cwd": "C:/path/to/united-cellars",
      "env": {
        "NODE_ENV": "production",
        "MCP_MODE": "true"
      }
    }
  }
}
```

3. Restart Claude Desktop to load the MCP server.

### Custom MCP Client

```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

// Create MCP client
const transport = new StdioClientTransport({
  command: 'node',
  args: ['dist/main.js'],
  cwd: '/path/to/united-cellars'
});

const client = new Client({
  name: 'wine-client',
  version: '1.0.0'
}, {
  capabilities: {
    tools: {}
  }
});

// Connect and use
await client.connect(transport);

// List available tools
const tools = await client.listTools();
console.log('Available tools:', tools);

// Call a tool
const result = await client.callTool({
  name: 'search_wines',
  arguments: {
    query: 'Bordeaux',
    limit: 5
  }
});

console.log('Search results:', result);
```

## 🔐 Authentication

### Public Tools
These tools can be called without authentication:
- `search_wines`
- `get_wine_details`
- `get_user_stats`

### Authenticated Tools
These tools require JWT authentication:
- `get_valuations`
- `get_valuation_details`
- `create_wine`

### Using Authentication
```bash
# Get JWT token first
TOKEN=$(curl -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}' \
  | jq -r '.accessToken')

# Use token in MCP tool call
curl -X POST "http://localhost:3000/api/v1/mcp/tools/call" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "create_wine",
    "arguments": {
      "name": "Test Wine",
      "producer": "Test Producer",
      "region": "Test Region",
      "country": "France",
      "vintage": 2020,
      "type": "red"
    }
  }'
```

## 🚨 Error Handling

### HTTP Errors
```json
{
  "success": false,
  "statusCode": 400,
  "message": "Tool execution failed",
  "errors": {
    "tool": "search_wines",
    "reason": "Invalid arguments"
  }
}
```

### MCP Tool Errors
```json
{
  "content": [
    {
      "type": "text",
      "text": "Tool execution failed: Wine not found"
    }
  ],
  "isError": true
}
```

### WebSocket Errors
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32601,
    "message": "Method not found"
  }
}
```

## 📊 Monitoring

### Health Check
```bash
curl -X GET "http://localhost:3000/api/v1/health"
```

### MCP Server Status
```bash
curl -X GET "http://localhost:3000/api/v1/mcp/info"
```

### Logs
Check application logs for MCP-related events:
```bash
tail -f logs/application-$(date +%Y-%m-%d).log | grep MCP
```

---

**Ready to integrate with AI assistants! 🤖🍷**
