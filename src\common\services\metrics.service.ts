import { Injectable } from '@nestjs/common';
import { LoggerService } from './logger.service';

interface MetricData {
  name: string;
  value: number;
  tags?: Record<string, string>;
  timestamp?: Date;
}

interface RequestMetric {
  method: string;
  endpoint: string;
  statusCode: number;
  responseTime: number;
  timestamp: Date;
}

@Injectable()
export class MetricsService {
  private metrics: Map<string, MetricData[]> = new Map();
  private requestMetrics: RequestMetric[] = [];

  constructor(private logger: LoggerService) {}

  // Record a custom metric
  recordMetric(name: string, value: number, tags?: Record<string, string>) {
    const metric: MetricData = {
      name,
      value,
      tags,
      timestamp: new Date(),
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    this.metrics.get(name)!.push(metric);
    
    // Keep only last 1000 entries per metric
    const metricArray = this.metrics.get(name)!;
    if (metricArray.length > 1000) {
      metricArray.splice(0, metricArray.length - 1000);
    }

    this.logger.debug(`Metric recorded: ${name} = ${value}`, 'MetricsService');
  }

  // Record HTTP request metrics
  recordRequest(method: string, endpoint: string, statusCode: number, responseTime: number) {
    const requestMetric: RequestMetric = {
      method,
      endpoint,
      statusCode,
      responseTime,
      timestamp: new Date(),
    };

    this.requestMetrics.push(requestMetric);

    // Keep only last 10000 request metrics
    if (this.requestMetrics.length > 10000) {
      this.requestMetrics.splice(0, this.requestMetrics.length - 10000);
    }

    // Record as custom metrics for aggregation
    this.recordMetric('http_requests_total', 1, {
      method,
      endpoint,
      status_code: statusCode.toString(),
    });

    this.recordMetric('http_request_duration_ms', responseTime, {
      method,
      endpoint,
    });

    this.logger.logRequest(method, endpoint, statusCode, responseTime);
  }

  // Get metrics summary
  getMetricsSummary() {
    const summary: Record<string, any> = {};

    for (const [name, data] of this.metrics.entries()) {
      const values = data.map(d => d.value);
      const recent = data.filter(d => 
        Date.now() - d.timestamp!.getTime() < 60000 // Last minute
      );

      summary[name] = {
        total_count: values.length,
        recent_count: recent.length,
        sum: values.reduce((a, b) => a + b, 0),
        avg: values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0,
        min: values.length > 0 ? Math.min(...values) : 0,
        max: values.length > 0 ? Math.max(...values) : 0,
        recent_avg: recent.length > 0 
          ? recent.map(d => d.value).reduce((a, b) => a + b, 0) / recent.length 
          : 0,
      };
    }

    return summary;
  }

  // Get request metrics summary
  getRequestMetrics() {
    const now = Date.now();
    const lastMinute = this.requestMetrics.filter(r => now - r.timestamp.getTime() < 60000);
    const lastHour = this.requestMetrics.filter(r => now - r.timestamp.getTime() < 3600000);

    const statusCodes = this.requestMetrics.reduce((acc, r) => {
      acc[r.statusCode] = (acc[r.statusCode] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const avgResponseTime = this.requestMetrics.length > 0
      ? this.requestMetrics.reduce((sum, r) => sum + r.responseTime, 0) / this.requestMetrics.length
      : 0;

    return {
      total_requests: this.requestMetrics.length,
      requests_last_minute: lastMinute.length,
      requests_last_hour: lastHour.length,
      status_codes: statusCodes,
      avg_response_time_ms: Math.round(avgResponseTime * 100) / 100,
      endpoints: this.getEndpointStats(),
    };
  }

  private getEndpointStats() {
    const endpointStats: Record<string, any> = {};

    for (const metric of this.requestMetrics) {
      const key = `${metric.method} ${metric.endpoint}`;
      if (!endpointStats[key]) {
        endpointStats[key] = {
          count: 0,
          total_response_time: 0,
          status_codes: {},
        };
      }

      endpointStats[key].count++;
      endpointStats[key].total_response_time += metric.responseTime;
      endpointStats[key].status_codes[metric.statusCode] = 
        (endpointStats[key].status_codes[metric.statusCode] || 0) + 1;
    }

    // Calculate averages
    for (const [endpoint, stats] of Object.entries(endpointStats)) {
      (stats as any).avg_response_time = Math.round(
        ((stats as any).total_response_time / (stats as any).count) * 100
      ) / 100;
      delete (stats as any).total_response_time;
    }

    return endpointStats;
  }

  // Get system health metrics
  getHealthMetrics() {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();

    return {
      uptime_seconds: Math.floor(uptime),
      memory_usage: {
        rss: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100, // MB
        heap_used: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
        heap_total: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
        external: Math.round(memUsage.external / 1024 / 1024 * 100) / 100, // MB
      },
      cpu_usage: process.cpuUsage(),
      node_version: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  }
}
