import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ValuationService } from './valuation.service';
import { CreateValuationDto } from './dto/create-valuation.dto';
import { UpdateValuationDto } from './dto/update-valuation.dto';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/users/entities/user.entity';
import { ValuationStatus } from './entities/valuation.entity';

@ApiTags('Valuations')
@ApiBearerAuth('JWT-auth')
@UseGuards(ThrottlerGuard, JwtAuthGuard)
@Controller({ path: 'valuations', version: '1' })
export class ValuationController {
  constructor(private readonly valuationService: ValuationService) { }

  @Post()
  @ApiOperation({ summary: 'Create a new valuation request' })
  @ApiResponse({ status: 201, description: 'Valuation created successfully' })
  create(@Body() createValuationDto: CreateValuationDto, @Request() req) {
    return this.valuationService.create(createValuationDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all valuations with pagination and filtering' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'status', required: false, enum: ValuationStatus, description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'Valuations retrieved successfully' })
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: ValuationStatus,
    @Request() req?,
  ) {
    return this.valuationService.findAll(page, limit, req.user.id, req.user.role, status);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get valuation by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Valuation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Valuation not found' })
  findOne(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.valuationService.findOne(id, req.user.id, req.user.role);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update valuation by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Valuation updated successfully' })
  @ApiResponse({ status: 404, description: 'Valuation not found' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateValuationDto: UpdateValuationDto,
    @Request() req,
  ) {
    return this.valuationService.update(id, updateValuationDto, req.user.id, req.user.role);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update valuation status' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Status updated successfully' })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() statusDto: { status: ValuationStatus; comment?: string },
    @Request() req,
  ) {
    return this.valuationService.updateStatus(
      id,
      statusDto.status,
      req.user.id,
      req.user.role,
      statusDto.comment,
    );
  }

  @Post(':id/assign')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Assign appraiser to valuation' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Appraiser assigned successfully' })
  assignAppraiser(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignDto: { appraiserId: string },
    @Request() req,
  ) {
    return this.valuationService.assignAppraiser(id, assignDto.appraiserId, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete valuation by ID' })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Valuation deleted successfully' })
  @ApiResponse({ status: 404, description: 'Valuation not found' })
  remove(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.valuationService.remove(id, req.user.id, req.user.role);
  }
}
