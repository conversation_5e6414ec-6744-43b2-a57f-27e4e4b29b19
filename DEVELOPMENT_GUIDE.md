# United Cellars - Quick Development Guide

## 🚀 Getting Started

### Prerequisites
- Node.js v18+
- PostgreSQL v12+
- npm or yarn

### Setup
```bash
# 1. Install dependencies
npm install

# 2. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 3. Run migrations and seeds
npm run migration:run
npm run seed:run

# 4. Start development server
npm run start:dev
```

### Default Login Credentials
```
Admin: <EMAIL> / Admin123!
Appraiser: <EMAIL> / Password123!
Customer: <EMAIL> / Password123!
```

### MCP Server Endpoints
```
HTTP: http://localhost:3000/api/v1/mcp
WebSocket: ws://localhost:3001/mcp
Documentation: http://localhost:3000/api/docs#/MCP%20(Model%20Context%20Protocol)
```

---

## 📋 Common Development Tasks

### 1. Creating a New Feature Module

```bash
# Generate module structure
nest g module modules/feature-name
nest g controller modules/feature-name
nest g service modules/feature-name

# Create entity file
touch src/modules/feature-name/entities/feature-name.entity.ts

# Create DTOs
mkdir src/modules/feature-name/dto
touch src/modules/feature-name/dto/create-feature-name.dto.ts
touch src/modules/feature-name/dto/update-feature-name.dto.ts
touch src/modules/feature-name/dto/feature-name-response.dto.ts
```

### 2. Database Operations

```bash
# Create migration
npm run migration:generate -- --name create-table-name

# Run migrations
npm run migration:run

# Revert last migration
npm run migration:revert

# Create seeder
npx sequelize-cli seed:generate --name demo-table-name

# Run seeders
npm run seed:run

# Revert seeders
npm run seed:revert
```

### 3. Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run e2e tests
npm run test:e2e

# Generate coverage report
npm run test:cov
```

---

## 🏗️ Project Structure

```
src/
├── common/                 # Shared utilities
│   ├── decorators/        # Custom validation decorators
│   ├── dto/              # Common DTOs
│   ├── filters/          # Exception filters
│   ├── interceptors/     # Response interceptors
│   └── services/         # Common services
├── config/               # Configuration
│   └── database/         # Database config
├── modules/              # Feature modules
│   ├── auth/            # Authentication
│   ├── users/           # User management
│   ├── valuation/       # Wine & valuation
│   └── health/          # Health checks
├── database/            # Database files
│   ├── migrations/      # Schema migrations
│   └── seeders/         # Seed data
├── app.module.ts        # Root module
└── main.ts             # Entry point
```

---

## 🔧 Code Templates

### Entity Template
```typescript
import {
  Table, Column, Model, DataType, PrimaryKey,
  Default, AllowNull, CreatedAt, UpdatedAt, DeletedAt
} from 'sequelize-typescript';

@Table({
  tableName: 'table_name',
  timestamps: true,
  paranoid: true,
})
export class EntityName extends Model<EntityName> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  name: string;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;

  @DeletedAt
  deletedAt: Date;
}
```

### Controller Template
```typescript
import {
  Controller, Get, Post, Body, Patch, Param, Delete,
  UseGuards, Request, ParseUUIDPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';

@ApiTags('Feature')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard)
@Controller({ path: 'feature', version: '1' })
export class FeatureController {
  constructor(private readonly featureService: FeatureService) {}

  @Post()
  @ApiOperation({ summary: 'Create feature' })
  create(@Body() createDto: CreateFeatureDto, @Request() req) {
    return this.featureService.create(createDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all features' })
  findAll(@Request() req) {
    return this.featureService.findAll(req.user.id, req.user.role);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get feature by ID' })
  findOne(@Param('id', ParseUUIDPipe) id: string, @Request() req) {
    return this.featureService.findOne(id, req.user.id, req.user.role);
  }
}
```

### Service Template
```typescript
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LoggerService } from '@/common/services/logger.service';

@Injectable()
export class FeatureService {
  constructor(
    @InjectModel(FeatureEntity)
    private featureModel: typeof FeatureEntity,
    private logger: LoggerService,
  ) {}

  async create(createDto: CreateFeatureDto, userId: string) {
    try {
      const feature = await this.featureModel.create({
        ...createDto,
        userId,
      });

      this.logger.log(`Feature created: ${feature.id}`, 'FeatureService');
      return feature;
    } catch (error) {
      this.logger.error(`Failed to create feature: ${error.message}`, error.stack, 'FeatureService');
      throw error;
    }
  }

  async findOne(id: string, userId: string, userRole: UserRole) {
    const feature = await this.featureModel.findByPk(id);
    
    if (!feature) {
      throw new NotFoundException('Feature not found');
    }

    return feature;
  }
}
```

---

## 🔍 Debugging Tips

### 1. Database Queries
Enable SQL logging in `.env`:
```
DB_LOGGING=true
```

### 2. Application Logs
Check logs in the `logs/` directory:
```bash
tail -f logs/application-$(date +%Y-%m-%d).log
tail -f logs/error-$(date +%Y-%m-%d).log
```

### 3. Health Check
```bash
curl http://localhost:3000/api/v1/health
```

### 4. API Testing
Use the Swagger UI at: `http://localhost:3000/api/docs`

### 5. MCP Testing
```bash
# Test MCP server info
curl http://localhost:3000/api/v1/mcp/info

# Test MCP tool execution
curl -X POST http://localhost:3000/api/v1/mcp/tools/call/public \
  -H "Content-Type: application/json" \
  -d '{"name":"search_wines","arguments":{"query":"Bordeaux"}}'
```

---

## 🚨 Common Issues & Solutions

### 1. Port Already in Use
```bash
# Find process using port 3000
netstat -ano | findstr :3000

# Kill the process (replace PID)
taskkill /PID <PID> /F
```

### 2. Database Connection Issues
- Check PostgreSQL is running
- Verify credentials in `.env`
- Ensure database exists

### 3. Migration Errors
```bash
# Reset database (development only)
npm run migration:revert
npm run migration:run
```

### 4. Module Import Errors
- Check path aliases in `tsconfig.json`
- Verify module exports
- Ensure proper imports in `app.module.ts`

---

## 📚 Useful Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [Sequelize Documentation](https://sequelize.org/docs/v6/)
- [Swagger/OpenAPI](https://swagger.io/docs/)
- [Jest Testing](https://jestjs.io/docs/getting-started)

---

## 🎯 Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Develop Feature**
   - Create module structure
   - Write entity and DTOs
   - Implement service logic
   - Create controller endpoints
   - Add Swagger documentation

3. **Database Changes**
   - Create migration
   - Update seeders if needed
   - Test migration up/down

4. **Testing**
   - Write unit tests
   - Test API endpoints
   - Verify error handling

5. **Code Quality**
   ```bash
   npm run lint
   npm run format
   npm run test
   ```

6. **Commit & Push**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/new-feature
   ```

---

**Happy Development! 🚀**
