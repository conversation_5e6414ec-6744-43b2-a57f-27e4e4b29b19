# United Cellars Valuation API

A comprehensive NestJS-based API for wine valuation and management system with user authentication, role-based access control, and comprehensive wine database.

## 🚀 Features

### Core Features
- **User Management**: Registration, authentication, and profile management
- **Wine Database**: Comprehensive wine catalog with detailed information
- **Valuation System**: Professional wine valuation requests and tracking
- **Role-Based Access**: Admin, User, and Appraiser roles with different permissions
- **API Versioning**: Versioned API endpoints (v1)
- **Real-time Logging**: Winston-based logging with file rotation
- **Metrics & Monitoring**: Application performance metrics and health checks
- **Rate Limiting**: Request throttling for API protection
- **MCP Server**: Model Context Protocol server for AI assistant integration

### Technical Features
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **PostgreSQL Database**: Robust relational database with Sequelize ORM
- **Swagger Documentation**: Comprehensive API documentation
- **Input Validation**: Class-validator based request validation
- **Error Handling**: Centralized error handling with detailed responses
- **Database Migrations**: Version-controlled database schema changes
- **Seed Data**: Initial data setup for development and testing

## 🛠️ Technology Stack

- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT with Passport.js
- **Documentation**: Swagger/OpenAPI
- **Logging**: Winston with daily file rotation
- **Validation**: Class-validator and Class-transformer
- **Security**: Helmet, CORS, Rate limiting
- **Testing**: Jest for unit and integration tests

## 📋 Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn package manager

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone <repository-url>
cd united-cellars
npm install
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb united_cellars

# Update database credentials in .env file
cp .env.example .env
# Edit .env with your database credentials
```

### 3. Run Migrations and Seeds
```bash
# Run database migrations
npm run migration:run

# Seed initial data
npm run seed:run
```

### 4. Start the Application
```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

The API will be available at:
- **API Base URL**: http://localhost:3000/api
- **Swagger Documentation**: http://localhost:3000/api/docs
- **MCP HTTP Endpoint**: http://localhost:3000/api/v1/mcp
- **MCP WebSocket Endpoint**: ws://localhost:3001/mcp

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user profile

### User Management
- `GET /api/v1/users` - List users (Admin/Appraiser only)
- `POST /api/v1/users` - Create user (Admin only)
- `GET /api/v1/users/:id` - Get user by ID
- `PATCH /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user (Admin only)
- `GET /api/v1/users/stats` - User statistics (Admin only)

### Wine Management
- `GET /api/v1/wines` - List wines (Public)
- `POST /api/v1/wines` - Add new wine (Authenticated users)
- `GET /api/v1/wines/:id` - Get wine details (Public)
- `PATCH /api/v1/wines/:id` - Update wine (Admin/Appraiser only)
- `DELETE /api/v1/wines/:id` - Delete wine (Admin only)
- `GET /api/v1/wines/search` - Search wines (Public)

### Valuation System
- `GET /api/v1/valuations` - List valuations
- `POST /api/v1/valuations` - Create valuation request
- `GET /api/v1/valuations/:id` - Get valuation details
- `PATCH /api/v1/valuations/:id` - Update valuation
- `PATCH /api/v1/valuations/:id/status` - Update valuation status
- `POST /api/v1/valuations/:id/assign` - Assign appraiser (Admin only)
- `DELETE /api/v1/valuations/:id` - Delete valuation

### Health & Monitoring
- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed health information
- `GET /api/v1/health/metrics` - Application metrics

### MCP (Model Context Protocol)
- `GET /api/v1/mcp/info` - Get MCP server information
- `GET /api/v1/mcp/tools` - List available MCP tools
- `GET /api/v1/mcp/resources` - List available MCP resources
- `GET /api/v1/mcp/prompts` - List available MCP prompts
- `POST /api/v1/mcp/tools/call` - Execute MCP tool (authenticated)
- `POST /api/v1/mcp/tools/call/public` - Execute public MCP tool
- `WebSocket: ws://localhost:3001/mcp` - MCP WebSocket endpoint

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Default Users
After running seeds, you'll have these default users:

- **Admin**: <EMAIL> / Admin123!
- **Appraiser**: <EMAIL> / Password123!
- **Customer**: <EMAIL> / Password123!

## 🏗️ Project Structure

```
src/
├── common/                 # Shared utilities and decorators
│   ├── decorators/        # Custom validation decorators
│   ├── dto/              # Common DTOs
│   ├── filters/          # Exception filters
│   ├── interceptors/     # Response interceptors
│   └── services/         # Common services (Logger, Metrics)
├── config/               # Configuration modules
│   └── database/         # Database configuration
├── modules/              # Feature modules
│   ├── auth/            # Authentication module
│   ├── health/          # Health check module
│   ├── users/           # User management module
│   └── valuation/       # Wine and valuation modules
├── database/            # Database migrations and seeds
│   ├── migrations/      # Database migrations
│   └── seeders/         # Seed data
├── app.module.ts        # Root application module
└── main.ts             # Application entry point
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 📝 Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your-password
DB_DATABASE=united_cellars
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start:prod
```

### Docker (Optional)
```bash
# Build image
docker build -t united-cellars-api .

# Run container
docker run -p 3000:3000 united-cellars-api
```

## 📊 Monitoring

The application includes built-in monitoring features:

- **Health Checks**: `/api/v1/health` endpoints
- **Metrics Collection**: Request/response metrics
- **Logging**: Structured logging with Winston
- **Error Tracking**: Centralized error handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the Swagger documentation at `/api/docs`
- Review the application logs
- Check the health endpoints for system status
