import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectConnection } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { MetricsService } from '@/common/services/metrics.service';

@Injectable()
export class HealthService {
  constructor(
    private configService: ConfigService,
    @InjectConnection()
    private sequelize: Sequelize,
    private metricsService: MetricsService,
  ) {}

  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: this.configService.get<string>('NODE_ENV'),
    };
  }

  async getDetailedHealth() {
    const health = this.getHealth();
    
    // Check database connection
    let databaseStatus = 'ok';
    let databaseError = null;
    
    try {
      await this.sequelize.authenticate();
    } catch (error) {
      databaseStatus = 'error';
      databaseError = error.message;
    }

    // Get system metrics
    const systemMetrics = this.metricsService.getHealthMetrics();
    
    return {
      ...health,
      checks: {
        database: {
          status: databaseStatus,
          error: databaseError,
        },
      },
      system: systemMetrics,
    };
  }

  getMetrics() {
    return {
      system: this.metricsService.getHealthMetrics(),
      application: this.metricsService.getMetricsSummary(),
      requests: this.metricsService.getRequestMetrics(),
    };
  }
}
