import {
  Table,
  Column,
  Model,
  DataType,
  Primary<PERSON>ey,
  <PERSON>fault,
  Unique,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  HasMany,
  BeforeCreate,
  BeforeUpdate,
} from 'sequelize-typescript';
import * as bcrypt from 'bcryptjs';
import { Valuation } from '@/modules/valuation/entities/valuation.entity';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  APPRAISER = 'appraiser',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

@Table({
  tableName: 'users',
  timestamps: true,
  paranoid: true,
})
export class User extends Model<User> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  firstName: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  lastName: string;

  @Unique
  @AllowNull(false)
  @Column(DataType.STRING(255))
  email: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  password: string;

  @Column(DataType.STRING(20))
  phone: string;

  @Default(UserRole.USER)
  @Column(DataType.ENUM(...Object.values(UserRole)))
  role: UserRole;

  @Default(UserStatus.ACTIVE)
  @Column(DataType.ENUM(...Object.values(UserStatus)))
  status: UserStatus;

  @Column(DataType.DATE)
  lastLoginAt: Date;

  @Column(DataType.INET)
  lastLoginIp: string;

  @Column(DataType.DATE)
  emailVerifiedAt: Date;

  @Column(DataType.STRING(255))
  emailVerificationToken: string;

  @Column(DataType.STRING(255))
  passwordResetToken: string;

  @Column(DataType.DATE)
  passwordResetExpiresAt: Date;

  @Column(DataType.JSON)
  preferences: Record<string, any>;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;

  @DeletedAt
  deletedAt: Date;

  // Associations
  @HasMany(() => Valuation)
  valuations: Valuation[];

  // Hooks
  @BeforeCreate
  @BeforeUpdate
  static async hashPassword(instance: User) {
    if (instance.changed('password')) {
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
      instance.password = await bcrypt.hash(instance.password, saltRounds);
    }
  }

  // Instance methods
  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  toJSON() {
    const values = { ...this.get() };
    delete values.password;
    delete values.emailVerificationToken;
    delete values.passwordResetToken;
    return values;
  }
}
