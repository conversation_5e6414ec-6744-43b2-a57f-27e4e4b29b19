'use strict';

const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('Admin123!', 12);
    
    await queryInterface.bulkInsert('users', [
      {
        id: uuidv4(),
        first_name: 'System',
        last_name: 'Administrator',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        status: 'active',
        email_verified_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', {
      email: '<EMAIL>',
    });
  },
};
