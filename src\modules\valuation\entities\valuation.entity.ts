import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  DeletedAt,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { User } from '@/modules/users/entities/user.entity';
import { Wine } from './wine.entity';
import { ValuationHistory } from './valuation-history.entity';

export enum ValuationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum ValuationPurpose {
  INSURANCE = 'insurance',
  SALE = 'sale',
  PURCHASE = 'purchase',
  ESTATE = 'estate',
  DONATION = 'donation',
  OTHER = 'other',
}

@Table({
  tableName: 'valuations',
  timestamps: true,
  paranoid: true,
})
export class Valuation extends Model<Valuation> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  userId: string;

  @ForeignKey(() => Wine)
  @AllowNull(false)
  @Column(DataType.UUID)
  wineId: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  referenceNumber: string;

  @Default(ValuationStatus.DRAFT)
  @Column(DataType.ENUM(...Object.values(ValuationStatus)))
  status: ValuationStatus;

  @AllowNull(false)
  @Column(DataType.ENUM(...Object.values(ValuationPurpose)))
  purpose: ValuationPurpose;

  @AllowNull(false)
  @Column(DataType.INTEGER)
  quantity: number;

  @Column(DataType.STRING(255))
  condition: string;

  @Column(DataType.STRING(255))
  provenance: string;

  @Column(DataType.STRING(255))
  storage: string;

  @Column(DataType.DECIMAL(12, 2))
  estimatedValue: number;

  @Column(DataType.DECIMAL(12, 2))
  finalValue: number;

  @Column(DataType.STRING(3))
  currency: string;

  @Column(DataType.DATE)
  valuationDate: Date;

  @Column(DataType.DATE)
  expiryDate: Date;

  @Column(DataType.TEXT)
  notes: string;

  @Column(DataType.JSON)
  methodology: Record<string, any>;

  @Column(DataType.JSON)
  marketData: Record<string, any>;

  @Column(DataType.JSON)
  attachments: string[];

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  appraiserId: string;

  @Column(DataType.DATE)
  completedAt: Date;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;

  @DeletedAt
  deletedAt: Date;

  // Associations
  @BelongsTo(() => User, 'userId')
  user: User;

  @BelongsTo(() => User, 'appraiserId')
  appraiser: User;

  @BelongsTo(() => Wine)
  wine: Wine;

  @HasMany(() => ValuationHistory)
  history: ValuationHistory[];
}
